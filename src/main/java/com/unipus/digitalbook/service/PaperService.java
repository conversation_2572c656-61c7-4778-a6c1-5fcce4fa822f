package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.entity.paper.Paper;
import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.params.paper.PaperQueryParam;
import com.unipus.digitalbook.model.params.paper.question.PaperQuestionListParam;

import java.util.List;

/**
 * 试卷服务接口
 */
public interface PaperService {

    /**
     * 保存试卷
     * @param paper 试卷对象
     * @param userId 用户ID
     * @return 试卷ID
     */
    String savePaper(Paper paper, Long userId);

    /**
     * 保存试卷/题库题目列表及标签列表
     * @param paperQuestionListParam 题组参数对象
     * @param userId 用户ID
     * @return 题库ID
     */
    String saveQuestions(PaperQuestionListParam paperQuestionListParam, Long userId);

    /**
     * 获取默认版本试卷列表
     * @param param 试卷查询参数
     * @return 试卷对象列表
     */
    List<Paper> getDefaultVersionPaperList(PaperQueryParam param);

    /**
     * 获取试卷详情
     * @param paperId 试卷ID
     * @param versionNumber 版本号
     * @return 试卷对象
     */
    Paper getPaperDetail(String paperId, String versionNumber);

    /**
     * 获取题目列表
     * @param parentBizGroupId 父级业务ID(试卷Id/题库Id)
     * @param versionNumber 版本号
     * @return 题目列表
     */
    List<BigQuestionGroup> getQuestions(String parentBizGroupId, String versionNumber);

    /**
     * 删除试卷
     * @param paperIdId 试卷业务ID
     * @param userId 当前用户ID
     * @return 是否删除成功
     */
    Boolean deletePaper(String paperIdId, Long userId);

}
