package com.unipus.digitalbook.service.useraction.strategy.content;

import com.alibaba.fastjson2.TypeReference;
import com.unipus.digitalbook.dao.ChapterVersionPOMapper;
import com.unipus.digitalbook.dao.UserChapterProgressPOMapper;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.entity.action.*;
import com.unipus.digitalbook.model.entity.chapter.ChapterNode;
import com.unipus.digitalbook.model.enums.ContentTypeEnum;
import com.unipus.digitalbook.model.enums.MessageTopicEnum;
import com.unipus.digitalbook.model.events.UserActionEvent;
import com.unipus.digitalbook.model.po.action.UserChapterProgressPO;
import com.unipus.digitalbook.producer.TenantMessageProducer;
import com.unipus.digitalbook.publisher.UserActionPublisher;
import com.unipus.digitalbook.service.BookVersionService;
import com.unipus.digitalbook.service.ChapterService;
import com.unipus.digitalbook.service.useraction.strategy.node.NodeCompletionStrategyFactory;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.CompletableFuture;

@Component
@Slf4j
public class UserChapterAction implements UserContentAction {

    @Resource
    private NodeCompletionStrategyFactory nodeCompletionStrategyFactory;
    @Resource
    private UserActionPublisher userActionPublisher;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private ChapterService chapterService;

    @Resource
    private UserChapterProgressPOMapper userChapterProgressPOMapper;

    @Resource
    private BookVersionService bookVersionService;
    @Resource
    private TenantMessageProducer tenantMessageProducer;

    @Resource
    private ChapterVersionPOMapper chapterVersionPOMapper;

    @Override
    public Set<ContentTypeEnum> getTypes() {
        return Set.of(ContentTypeEnum.CHAPTER);
    }

    @Override
    public Response<Boolean> finishNode(UserAction userAction) {
        Long chapterVersionId = userAction.getContentVersionId();
        Map<String, ChapterNode> nodeMap = chapterService.getNodeMapCache(chapterVersionId);
        String nodeId = userAction.getNodeId();
        ChapterNode chapterNode = nodeMap.get(nodeId);
        if (chapterNode == null) {
            log.error("节点不存在, 或者已经变更 nodeId = {}", nodeId);
            return Response.fail("节点不存在");
        }
        ContentNode node = new ContentNode(chapterNode);
        boolean completed = nodeCompletionStrategyFactory.getStrategy(chapterNode.getType()).isCompleted(node, userAction);
        if (!completed) {
            log.debug("节点未完成 {}", chapterNode.getId());
            return Response.success(false);
        }
        String openId = userAction.getOpenId();
        String envPartition = userAction.getEnvPartition();
        Long tenantId = userAction.getTenantId();
        setFinishNode(tenantId, openId, chapterVersionId, envPartition, chapterNode.getOffset());
        userActionPublisher.send(new UserActionEvent(node, userAction));
        return Response.success(true);
    }

    @Override
    public Response<Boolean> finishQuestionNode(UserAction userAction) {
        Long chapterVersionId = userAction.getContentVersionId();
        String questionId = userAction.getQuestionId();
        ChapterNode chapterNode = chapterService.getQuestionNodeByChapterVersionId(chapterVersionId, questionId);
        if (chapterNode == null) {
            log.error("题目不存在, 或者已经变更 questionId = {}", questionId);
            return Response.fail("题目不存在");
        }
        ContentNode node = new ContentNode(chapterNode);
        Long tenantId = userAction.getTenantId();
        String openId = userAction.getOpenId();
        String envPartition = userAction.getEnvPartition();
        setFinishNode(tenantId, openId, chapterVersionId, envPartition, chapterNode.getOffset());
        userActionPublisher.send(new UserActionEvent(node, userAction));
        return Response.success(true);
    }

    @Override
    public Response<Boolean> postProcessNode(UserAction userAction, ContentNode contentNode) {
        saveProgress(userAction);
        Long chapterVersionId = userAction.getContentVersionId();
        String chapterId = userAction.getContentId();
        String openId = userAction.getOpenId();
        Long tenantId = userAction.getTenantId();
        String envPartition = userAction.getEnvPartition();
        // 如果章节发布的版本和写入的版本不一致，需要双写
        Long targetChapterVersionId = getTargetChapterVersionId(tenantId, chapterId);
        if (targetChapterVersionId != null && !Objects.equals(targetChapterVersionId, chapterVersionId)) {
            log.debug("diff chapter version openId:{} chapterId:{}, chapterVersionId: {}, {}", openId, chapterId, chapterVersionId, targetChapterVersionId);
            finishNodeAndSave(userAction.getTenantId(), openId, chapterId, targetChapterVersionId, contentNode.getId(), envPartition);
        }
        String chapterVersionMigrateKey = getChapterVersionMigrateKey(tenantId, chapterVersionId);
        long originChapterVersionId = NumberUtils.toLong(stringRedisTemplate.opsForValue().get(chapterVersionMigrateKey));
        // 章节正在迁移，但是还当前用户还未迁移完成，则提前进行迁移
        if (originChapterVersionId > 0) {
            log.debug("chapterVersionMigrate openId:{} chapterId:{}, chapterVersionId: {}, {}", openId, chapterId, chapterVersionId, originChapterVersionId);
            String chapterVersionMigrateSetKey = getChapterVersionMigrateSetKey(tenantId, chapterVersionId);
            if (!stringRedisTemplate.opsForSet().isMember(chapterVersionMigrateSetKey, openId)) {
                Map<String, ChapterNode> nodeMap = chapterService.getNodeMapCache(chapterVersionId);
                Map<String, ChapterNode> nodeMapOrigin = chapterService.getNodeMapCache(originChapterVersionId);
                migrateUserProgress(tenantId, envPartition, openId, chapterId, originChapterVersionId, nodeMapOrigin, targetChapterVersionId, nodeMap);
            }
        }
        // todo 待优化 通过租户配置 判断推送条件
        if (userAction.getTenantId() == 1) {
            syncSingleNode(userAction, contentNode);
            return Response.success(true);
        }
        syncMultiNode(userAction, contentNode);
        return Response.success(true);
    }

    @Override
    public Response<Boolean> postPublishProcess(Long tenantId, String contentId, Long contentVersionId) {
        // 获取当前学习的章节版本
        Long originalChapterVersionId = getTargetChapterVersionId(tenantId, contentId);
        if (!Objects.equals(originalChapterVersionId, contentVersionId)) {
            log.debug("diff chapter versionId: {},  {}, {}", contentId, originalChapterVersionId, contentVersionId);
            stringRedisTemplate.opsForValue().set(getTargetChapterVersionKey(tenantId, contentId), contentVersionId.toString());
            // 开始迁移数据
            migratedProgress(tenantId, contentId, originalChapterVersionId, contentVersionId);
        }
        return Response.success(true);
    }

    private List<NodeProgress> completionStatus(ChapterNode node, Map<String, ChapterNode> nodeMap, byte[] progressValue) {
        List<NodeProgress> nodeProgresses = new ArrayList<>();
        nodeProgresses.add(new NodeProgress(node.getId(), node.getType(), 1, 1, true));
        ChapterNode current = nodeMap.get(node.getParentId());
        int total = 0;
        int completedCount = 0;
        int maxLevel = 6;
        while (current != null && maxLevel > 0) {
            maxLevel--;
            total += current.getChildren().size();
            if (isCompletedNode(progressValue, current.getOffset())) {
                completedCount += current.getChildren().size();
                current = nodeMap.get(current.getParentId());
                continue;
            }
            completedCount += (int) current.getChildren().stream().filter(c -> isCompletedNode(progressValue, c.getOffset())).count();
            nodeProgresses.add(new NodeProgress(current.getId(), current.getType(), total, completedCount));
            current = nodeMap.get(current.getParentId());
        }
        return nodeProgresses;
    }

    private void syncSingleNode(UserAction userAction, ContentNode contentNode) {
        if (contentNode.isRealQuestionNode()) {
            return;
        }
        Long chapterVersionId = userAction.getContentVersionId();
        log.debug("sync book version: {}, {}, {}", userAction.getBookId(), userAction.getBookVersionNumber(), chapterVersionId);
        if (Optional.ofNullable(tenantMessageProducer.getTenantSubscribe(
                userAction.getTenantId(), MessageTopicEnum.PUSH_USER_PROGRESS)).isPresent()) {
            CompletableFuture<Response<String>> responseFuture = tenantMessageProducer.produceAsyncMessage(
                    userAction.getTenantId(),
                    MessageTopicEnum.PUSH_USER_PROGRESS,
                    new TypeReference<>() {
                    },
                    new UserContentProgressData(userAction, new NodeProgress(contentNode.getId(), contentNode.getType(), 1, 1, true)),
                    new TypeReference<>() {});
        } else {
            log.warn("租户未订阅消息主题: {}, 租户ID: {}", MessageTopicEnum.PUSH_USER_PROGRESS.name(), userAction.getTenantId());
        }
    }
    private void syncMultiNode(UserAction userAction, ContentNode contentNode) {
        if (Optional.ofNullable(tenantMessageProducer.getTenantSubscribe(
                userAction.getTenantId(), MessageTopicEnum.PUSH_USER_PROGRESS)).isPresent()) {
            Long tenantId = userAction.getTenantId();
            String openId = userAction.getOpenId();
            Long chapterVersionId = userAction.getContentVersionId();
            String envPartition = userAction.getEnvPartition();
            byte[] progressValue = getNodeProgress(tenantId, openId, chapterVersionId, envPartition);
            Map<String, ChapterNode> nodeMap = chapterService.getNodeMapCache(chapterVersionId);
            List<NodeProgress> nodeProgresses = completionStatus(nodeMap.get(contentNode.getId()), nodeMap, progressValue);
            int total = nodeMap.size();
            int finishedNodeCount = getFinishedNodeCount(userAction.getTenantId(), openId, chapterVersionId, envPartition);
            log.debug("total:{}, finishedNodeCount:{}", total, finishedNodeCount);
            nodeProgresses.forEach(nodeProgress -> {
                int completed = nodeProgress.getCompleted();
                tenantMessageProducer.produceAsyncMessage(
                        userAction.getTenantId(),
                        MessageTopicEnum.PUSH_USER_PROGRESS,
                        new TypeReference<Response<String>>() {
                        },
                        new UserContentProgressData(userAction, nodeProgress, total, completed),
                        new TypeReference<>() {});
            });
        } else {
            log.warn("租户未订阅消息主题: {}, 租户ID: {}", MessageTopicEnum.PUSH_USER_PROGRESS.name(), userAction.getTenantId());
        }

    }
    private Long getTargetChapterVersionId(Long tenantId, String chapterId) {
        String s = stringRedisTemplate.opsForValue().get(getTargetChapterVersionKey(tenantId, chapterId));
        if (!StringUtils.hasText(s)) {
            return null;
        }
        return Long.valueOf(s);
    }

    private Response<Boolean> finishNodeAndSave(Long tenantId, String openId, String chapterId, Long chapterVersionId, String nodeId, String envPartition) {
        Map<String, ChapterNode> nodeMap = chapterService.getNodeMapCache(chapterVersionId);
        ChapterNode chapterNode = nodeMap.get(nodeId);
        if (chapterNode == null) {
            log.debug("节点可能已变更 {}", nodeId);
            return Response.fail("节点可能已变更");
        }
        setFinishNode(tenantId, openId, chapterVersionId, envPartition, chapterNode.getOffset());
        byte[] bit = getNodeProgress(tenantId, openId, chapterVersionId, envPartition);
        userChapterProgressPOMapper.saveProgressBit(tenantId, openId, chapterId, chapterVersionId, envPartition, bit);
        return Response.success(true);
    }

    /**
     * 持久化进度
     * @param userAction
     * @return
     */
    private Response<Boolean> saveProgress(UserAction userAction) {
        Long tenantId = userAction.getTenantId();
        String openId = userAction.getOpenId();
        Long chapterVersionId = userAction.getContentVersionId();
        String chapterId = userAction.getContentId();
        String envPartition = userAction.getEnvPartition();
        byte[] bit = getNodeProgress(tenantId, openId, chapterVersionId, envPartition);
        userChapterProgressPOMapper.saveProgressBit(tenantId, openId, chapterId, chapterVersionId, envPartition, bit);
        return Response.success(true);
    }

    private Response<Boolean> migratedProgress(Long tenantId, String chapterId, Long originChapterVersionId, Long chapterVersionId) {
        if (originChapterVersionId == null) {
            log.debug("没有需要迁移的章节进度 {}", chapterId);
            return Response.success(true);
        }
        String chapterVersionMigrateKey = getChapterVersionMigrateKey(tenantId, chapterVersionId);
        stringRedisTemplate.opsForValue().set(chapterVersionMigrateKey, String.valueOf(originChapterVersionId));
        Map<String, ChapterNode> nodeMap = chapterService.getNodeMapCache(chapterVersionId);
        Map<String, ChapterNode> nodeMapOrigin = chapterService.getNodeMapCache(originChapterVersionId);
        log.debug("migrate chapterId: {}, {}, {}", chapterId, originChapterVersionId, chapterVersionId);
        // 获取章节下所有用户当前章节的进度数据
        Long lastId = 0L;
        List<UserChapterProgressPO> progressList = userChapterProgressPOMapper.getProgressList(tenantId, originChapterVersionId, lastId);
        while (!progressList.isEmpty()) {
            progressList.forEach(progress -> {
                // 开始迁移用户章节数据
                migrateUserProgress(tenantId, progress.getEnvPartition(), progress.getOpenId(), chapterId, originChapterVersionId, nodeMapOrigin, chapterVersionId, nodeMap);
            });
            log.debug("progressList size: {}, {}", progressList.size(), lastId);
            lastId = progressList.getLast().getId();
            progressList = userChapterProgressPOMapper.getProgressList(tenantId, originChapterVersionId, lastId);
        }
        stringRedisTemplate.delete(chapterVersionMigrateKey);
        log.debug("migrate chapterId success: {}, {}, {}", chapterId, originChapterVersionId, chapterVersionId);
        return Response.success();
    }

    private void migrateUserProgress(Long tenantId, String envPartition, String openId, String chapterId, Long originChapterVersionId, Map<String, ChapterNode> nodeMapOrigin, Long targetChapterVersionId, Map<String, ChapterNode> nodeMap) {
        String userMigrateKey = getChapterVersionMigrateSetKey(tenantId, targetChapterVersionId);
        if (stringRedisTemplate.opsForSet().isMember(userMigrateKey, openId)) {
            log.debug("migrate user progressed: {}, {}, {}, {}, {}", tenantId, openId, chapterId, originChapterVersionId, targetChapterVersionId);
            return;
        }
        log.debug("migrate user progress: {}, {}, {}, {}, {}", tenantId, openId, chapterId, originChapterVersionId, targetChapterVersionId);
        byte[] progressValue = getNodeProgress(tenantId, openId, originChapterVersionId, envPartition);
        byte[] progressRawKey = ((RedisSerializer<String>)stringRedisTemplate.getKeySerializer())
                .serialize(getProgressKey(tenantId, openId, targetChapterVersionId, envPartition));
        stringRedisTemplate.executePipelined((RedisCallback<?>) connection -> {
            nodeMap.forEach((id, node) -> {
                ChapterNode chapterNode = nodeMapOrigin.get(id);
                if (chapterNode == null) {
                    log.debug("迁移节点可能已变更 {}", id);
                    return;
                }
                if (isCompletedNode(progressValue, chapterNode.getOffset())) {
                    connection.stringCommands().setBit(
                            progressRawKey,
                            node.getOffset(),
                            true
                    );
                }
            });
            return null;
        });
        byte[] bit = getNodeProgress(tenantId, openId, targetChapterVersionId, envPartition);
        userChapterProgressPOMapper.saveProgressBit(tenantId, openId, chapterId, targetChapterVersionId, envPartition, bit);
        stringRedisTemplate.opsForSet().add(userMigrateKey, openId);
        stringRedisTemplate.expire(userMigrateKey, Duration.ofDays(1));
    }



    private int getFinishedNodeCount(Long tenantId, String openId, Long chapterVersionId, String envPartition) {
        String progressKey = getProgressKey(tenantId, openId, chapterVersionId, envPartition);
        byte[] progressRawKey = ((RedisSerializer<String>)stringRedisTemplate.getKeySerializer())
                .serialize(progressKey);
        Long count = stringRedisTemplate.execute(
                (RedisCallback<Long>) connection -> connection.stringCommands().bitCount(progressRawKey)
        );
        return count.intValue();
    }

    private Boolean setFinishNode(Long tenantId, String openId, Long chapterVersionId, String envPartition, int offset) {
        String progressKey = getProgressKey(tenantId, openId, chapterVersionId, envPartition);
        stringRedisTemplate.opsForValue().setBit(progressKey, offset, true);
        stringRedisTemplate.expire(progressKey, Duration.ofDays(7));
        return true;
    }

    private byte[] getNodeProgress(Long tenantId, String openId, Long chapterVersionId, String envPartition) {
        String progressKey = getProgressKey(tenantId, openId, chapterVersionId, envPartition);
        byte[] progressRawKey = ((RedisSerializer<String>)stringRedisTemplate.getKeySerializer())
                .serialize(progressKey);
        if (!stringRedisTemplate.hasKey(progressKey)) {
            byte[] progressBit = userChapterProgressPOMapper.getProgressBit(tenantId, openId, chapterVersionId, envPartition);
            batchSetBit(progressBit, progressRawKey);
        }
        return stringRedisTemplate.execute((RedisCallback<byte[]>) connection ->
                connection.stringCommands().get(progressRawKey)
        );
    }
    private void batchSetBit(byte[] progressBit, byte[] progressRawKey) {
        if (progressBit == null || progressRawKey == null) return;
        stringRedisTemplate.executePipelined((RedisCallback<Object>) connection -> {
            for (int byteIndex = 0; byteIndex < progressBit.length; byteIndex++) {
                byte b = progressBit[byteIndex];
                if (b == 0) continue;
                for (int bit = 0; bit < 8; bit++) {
                    boolean bitValue = ((b >> (7 - bit)) & 1) == 1;
                    if (!bitValue) continue;
                    int bitIndex = byteIndex * 8 + bit;
                    connection.stringCommands().setBit(
                            progressRawKey,
                            bitIndex,
                            true
                    );
                }
            }
            return null;
        });
    }
    private String getProgressKey(Long tenantId, String openId, Long chapterVersionId, String envPartition) {
        if (envPartition == null) {
            return String.format("progress:%d:%s:%d", tenantId, openId, chapterVersionId);
        }
        return String.format("progress:%d:%s:%s:%d", tenantId, envPartition, openId, chapterVersionId);
    }

    private String getTargetChapterVersionKey(Long tenantId, String chapterId) {
        return String.format("targetChapterVersion:%d:%s", tenantId, chapterId);
    }

    private String getChapterVersionMigrateSetKey(Long tenantId, Long chapterVersionId) {
        return String.format("userMigrateUserSetKey:%d:%d", tenantId, chapterVersionId);
    }

    private String getChapterVersionMigrateKey(Long tenantId, Long chapterVersionId) {
        return String.format("userMigrateKey:%d:%d", tenantId, chapterVersionId);
    }
}
