package com.unipus.digitalbook.service.useraction.strategy.node;

import com.unipus.digitalbook.model.entity.action.ContentNode;
import com.unipus.digitalbook.model.entity.action.UserAction;
import com.unipus.digitalbook.model.enums.QuestionGroupTypeEnum;
import org.springframework.stereotype.Component;
import java.util.Set;

@Component
public class QuestionNodeStrategy implements CompletionStrategy{
    @Override
    public Set<String> getTypes() {
        return Set.of("question-block");
    }

    @Override
    public boolean isCompleted(ContentNode node, UserAction userAction) {
        // 精读课文上报直接完成
        if (QuestionGroupTypeEnum.RICH_TEXT_READ.getName().equals(node.getQuestionType())) {
            return true;
        }
        return false;
    }
}
