package com.unipus.digitalbook.service.validator.tenant;

import com.unipus.digitalbook.model.enums.ChannelEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class MessageValidatorFactory {
    private final Map<ChannelEnum, TenantChannelValidator> factories = new HashMap<>();

    @Autowired
    public MessageValidatorFactory(ApplicationContext applicationContext) {
        Map<String, TenantChannelValidator> processorBeans =
                applicationContext.getBeansOfType(TenantChannelValidator.class);
        for (Map.Entry<String, TenantChannelValidator> entry : processorBeans.entrySet()) {
            TenantChannelValidator validator = entry.getValue();
            factories.put(validator.supportChannel(), validator);
        }
    }

    public TenantChannelValidator getValidator(ChannelEnum channel) {
        return factories.get(channel);
    }
}
