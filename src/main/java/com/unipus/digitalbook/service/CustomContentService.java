package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.entity.content.CopyContentItem;
import com.unipus.digitalbook.model.entity.content.CustomContent;
import com.unipus.digitalbook.model.entity.content.CustomContentNode;
import com.unipus.digitalbook.model.entity.content.PublishContentItem;
import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;

import java.util.List;
import java.util.Map;

public interface CustomContentService {

    /**
     * 保存自建内容
     *
     * @param customContent   自建内容实体
     * @param contentPackage  内容数据包
     * @param dataPackage     数据包
     * @return 保存结果
     */
    Boolean saveCustomContent(CustomContent customContent, String contentPackage, String dataPackage);

    /**
     * 批量保存自建内容
     *
     * @param contentPackageMap 自建内容与内容数据包的映射
     * @param dataPackage       数据包
     * @return 保存结果
     */
    Boolean saveBatchCustomContent(Map<CustomContent, String> contentPackageMap, String dataPackage);

    /**
     * 更新自建内容名称
     *
     * @param customContent   自建内容实体
     * @param contentPackage  内容数据包
     * @param dataPackage     数据包
     * @return 更新结果
     */
    Boolean updateCustomContentName(CustomContent customContent, String contentPackage, String dataPackage);

    /**
     * 根据业务ID获取编写中的自建内容
     *
     * @param bizId    业务ID
     * @param tenantId 租户ID
     * @return 自建内容实体
     */
    CustomContent getEditingCustomContentByBizId(String bizId, Long tenantId);

    /**
     * 根据业务ID列表批量获取编写中的自建内容
     *
     * @param bizIds   业务ID列表
     * @param tenantId 租户ID
     * @return 自建内容列表
     */
    List<CustomContent> getEditingCustomContentByBizIds(List<String> bizIds, Long tenantId);

    /**
     * 根据业务ID获取编写中的自建内容简化信息（不包含content、studentContent）
     *
     * @param bizId    业务ID
     * @param tenantId 租户ID
     * @return 自建内容实体
     */
    CustomContent getEditingCustomContentSimpleByBizId(String bizId, Long tenantId);

    /**
     * 根据业务ID列表批量获取编写中的自建内容简化信息（不包含content、studentContent）
     *
     * @param bizIds   业务ID列表
     * @param tenantId 租户ID
     * @return 自建内容列表
     */
    List<CustomContent> getEditingCustomContentSimpleByBizIds(List<String> bizIds, Long tenantId);

    /**
     * 根据业务ID获取已发布的自建内容
     *
     * @param bizId    业务ID
     * @param tenantId 租户ID
     * @return 自建内容实体
     */
    CustomContent getPublishedCustomContentByBizId(String bizId, Long tenantId);

    /**
     * 根据业务ID列表批量获取已发布的自建内容
     *
     * @param bizIds   业务ID列表
     * @param tenantId 租户ID
     * @return 自建内容列表
     */
    List<CustomContent> getPublishedCustomContentByBizIds(List<String> bizIds, Long tenantId);

    /**
     * 根据业务ID获取已发布的自建内容简化信息（不包含content、studentContent）
     *
     * @param bizId    业务ID
     * @param tenantId 租户ID
     * @return 自建内容实体
     */
    CustomContent getPublishedCustomContentSimpleByBizId(String bizId, Long tenantId);

    /**
     * 根据业务ID列表批量获取已发布的自建内容简化信息（不包含content、studentContent）
     *
     * @param bizIds   业务ID列表
     * @param tenantId 租户ID
     * @return 自建内容列表
     */
    List<CustomContent> getPublishedCustomContentSimpleByBizIds(List<String> bizIds, Long tenantId);

    /**
     * 根据业务ID列表批量删除自建内容
     *
     * @param bizIds      业务ID列表
     * @param tenantId    租户ID
     * @param userId      用户ID
     * @param dataPackage 数据包
     * @return 删除结果
     */
    Boolean deleteCustomContentByBizIds(List<String> bizIds, Long tenantId, Long userId, String dataPackage);

    /**
     * 发布自建内容
     *
     * @param publishContentList 要发布的自建内容列表
     * @param deleteContentList  要删除的自建内容列表
     * @param tenantId           租户ID
     * @param userId             用户ID
     * @param dataPackage        数据包
     * @return 发布结果
     */
    Boolean publishCustomContent(List<PublishContentItem> publishContentList, List<PublishContentItem> deleteContentList, Long tenantId, Long userId, String dataPackage);


    /**
     * 根据业务ID获取节点信息
     *
     * @param bizId 业务ID
     * @return 节点信息
     */
    Map<String, CustomContentNode> getNodeMapByBizId(String bizId);


    /**
     * 根据业务ID获取题目节点信息
     *
     * @param bizId 业务ID
     * @return 问题节点信息
     */
    Map<String, CustomContentNode> getQuestionNodeMapByBizId(String bizId);

    /**
     * 复制自建内容
     *
     * @param copyContentList 自建内容复制项列表
     * @param status          内容状态（0：编写中/1：待发布/2：已发布）
     * @param tenantId        租户ID
     * @param userId          当前用户ID
     * @param dataPackage     数据包
     * @return 复制前后bizId的映射关系 Map<原bizId, 新bizId>
     */
    Map<String, String> copyCustomContentByBizIds(List<CopyContentItem> copyContentList, Integer status, Long tenantId, Long userId, String dataPackage);

    /**
     * 根据内容ID获取题目列表
     *
     * @param contentId 内容ID
     * @param tenantId  租户ID
     * @return 题目列表
     */
    List<BigQuestionGroup> getQuestionListByContentId(Long contentId, Long tenantId);

    /**
     * 根据内容ID列表获取题目映射关系
     *
     * @param contentIds 内容ID列表
     * @param tenantId   租户ID
     * @return 内容ID到题目列表的映射关系 Map<内容ID, 题目列表>
     */
    Map<Long, List<BigQuestionGroup>> getQuestionMapByContentIds(List<Long> contentIds, Long tenantId);

    /**
     * 根据业务ID列表批量获取编写中的自建内容目录信息
     *
     * @param bizIds   业务ID列表
     * @param tenantId 租户ID
     * @return 自建内容目录信息列表
     */
    List<CustomContent> getEditingCustomContentCatalogByBizIds(List<String> bizIds, Long tenantId);

    /**
     * 根据业务ID列表批量获取已发布的自建内容目录信息
     *
     * @param bizIds   业务ID列表
     * @param tenantId 租户ID
     * @return 自建内容目录信息列表
     */
    List<CustomContent> getPublishedCustomContentCatalogByBizIds(List<String> bizIds, Long tenantId);
}