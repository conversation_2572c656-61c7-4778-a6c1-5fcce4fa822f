package com.unipus.digitalbook.service.factory.paper.answer;

import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.model.entity.tag.Tag;
import com.unipus.digitalbook.model.enums.TagResourceTypeEnum;
import com.unipus.digitalbook.service.TagService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * 标签处理器
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class TagProcessor {
    private final TagService tagService;

    /**
     * 处理知识点
     * @param questionIds 题目ID列表
     * @return 知识点信息映射（key：知识点(名称)，value:资源ID列表）
     */
    public List<Tag> processKnowledgePoints(List<String> questionIds, String versionNumber) {
        if (CollectionUtils.isEmpty(questionIds)) {
            return List.of();
        }

        Map<String, List<Tag>> tagsMap = tagService.getTagListByResourceIdsAndVersion(questionIds, TagResourceTypeEnum.QUESTION, versionNumber);
        if (CollectionUtils.isEmpty(tagsMap)) {
            log.debug("未找到对应的标签信息:{}, {}", questionIds, versionNumber);
            return List.of();
        }
        log.debug("诊断卷标签信息:{}", JsonUtil.toJsonString(tagsMap));

        // 按题目ID列表顺序输出知识点，并确保返回非空列表
        return questionIds.stream()
                .map(tagsMap::get)
                .filter(list -> !CollectionUtils.isEmpty(list))
                .flatMap(List::stream)
                .toList();
     }
}
