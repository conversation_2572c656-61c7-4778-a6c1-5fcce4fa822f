package com.unipus.digitalbook.service.factory.paper.answer;

import com.unipus.digitalbook.common.exception.business.BizException;
import com.unipus.digitalbook.common.utils.VirtualThreadPoolManager;
import com.unipus.digitalbook.model.entity.paper.PaperInstance;
import com.unipus.digitalbook.model.entity.paper.UserQuestionScore;
import com.unipus.digitalbook.model.entity.question.*;
import com.unipus.digitalbook.model.enums.QuestionGroupTypeEnum;
import com.unipus.digitalbook.service.UserAnswerService;
import jodd.util.ThreadUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 用户作答处理器
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class UserAnswerProcessor {

    private final UserAnswerService userAnswerService;
    private final VirtualThreadPoolManager virtualThreadPoolManager;

    // 主观题处理结果循环获取：重试次数
    private static final int RETRY_COUNT = 20;
    // 主观题处理结果循环获取：间隔(毫秒)
    private static final int RETRY_INTERVAL = 500;
    // 主观题处理结果循环获取：超时时间(毫秒)
    private static final int TIMEOUT = RETRY_COUNT * RETRY_INTERVAL;

    /**
     * 处理用户大题
     * @param groups 大题组对象
     * @param answersMap 用户题目解答列表
     * @return 用户作答（包含客观题分数）
     */
    public List<UserQuestionScore> processAnswers(List<BigQuestionGroup> groups, Map<String, List<UserAnswer>> answersMap) {

        if (CollectionUtils.isEmpty(groups) || answersMap == null) {
            return List.of();
        }

        // 用户作答记录（包含成绩）
        Map<String, UserAnswerList> userAnswerListMap = new HashMap<>();
        
        // 1.处理客观题作答并获取得分
        processObjectivesQuestions(groups, answersMap, userAnswerListMap);
        log.debug("客观题大题ID列表:{}", userAnswerListMap.keySet());

        // 2.处理主观题作答
        processSubjectiveQuestions(groups, answersMap, userAnswerListMap);

        // 3.保存作答记录并组装成绩映射
        return saveUserAnswersWithScore(groups, userAnswerListMap);
    }

    // 保存用户作答记录并组装成绩映射
    private List<UserQuestionScore> saveUserAnswersWithScore(List<BigQuestionGroup> groups, Map<String, UserAnswerList> userAnswerListMap) {
        // 用户小题维度成绩列表
        List<UserQuestionScore> scores = new ArrayList<>();

        for (BigQuestionGroup group : groups) {
            UserAnswerList userAnswerList = userAnswerListMap.get(group.getBizGroupId());
            if (userAnswerList != null && !CollectionUtils.isEmpty(userAnswerList.getUserAnswers())) {
                // 设置作答通过
                userAnswerList.getUserAnswers().forEach(ua->ua.setPass(Boolean.TRUE));
                // 保存用户作答记录
                userAnswerService.saveUserAnswers(userAnswerList.getUserAnswers());
                // 组装小题得分
                scores.addAll(UserQuestionScore.buildUserSmallQuestionScores(group, userAnswerList));
            }
        }
        return scores;
    }

    // 处理客观题
    private void processObjectivesQuestions(List<BigQuestionGroup> groups, Map<String, List<UserAnswer>> answersMap,
                                            Map<String, UserAnswerList> userAnswerListMap) {
        List<BigQuestionGroup> objectives = groups.stream()
                .filter(group -> QuestionGroupTypeEnum.isObjective(group.getType()))
                .toList();
        for (BigQuestionGroup group : objectives) {
            List<UserAnswer> answers = answersMap.getOrDefault(group.getBizGroupId(), new ArrayList<>());
            UserAnswerList userAnswerList = userAnswerService.judgeScore(group, new UserAnswerList(answers));
            userAnswerListMap.put(group.getBizGroupId(), userAnswerList);
        }
    }

    // 处理主观题
    private void processSubjectiveQuestions(List<BigQuestionGroup> groups, Map<String, List<UserAnswer>> answersMap,
                                            Map<String, UserAnswerList> userAnswerListMap) {
        List<BigQuestionGroup> subjectives = groups.stream()
                .filter(group -> !QuestionGroupTypeEnum.isObjective(group.getType()))
                .toList();
        if(CollectionUtils.isEmpty(subjectives)){
            log.debug("没有主观题");
            return;
        }

        // 使用统一的虚拟线程池管理器
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        for (BigQuestionGroup group : subjectives) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() ->
                    userAnswerListMap.put(group.getBizGroupId(), processSubjectiveQuestionsByAsyncJudge(group, answersMap)),
                    virtualThreadPoolManager.getBatchExecutor());
            futures.add(future);
        }

        try {
            // 等待所有判题任务完成，设置总超时时间
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
            allFutures.orTimeout(TIMEOUT, TimeUnit.MILLISECONDS).join();

            if (allFutures.isCompletedExceptionally()) {
                throw new BizException("主观题处理过程中发生异常");
            }
        } catch (CompletionException ex) {
            Throwable cause = ex.getCause();
            String message = cause instanceof TimeoutException ? "主观题处理超时，请稍后重试。" : "主观题处理发生异常，请稍后重试。";
            log.error(message, ex);
            throw new BizException(message);
        } catch (Exception ex) {
            String message = "主观题处理发生未知异常，请稍后重试。";
            log.error(message, ex);
            throw new BizException(String.format("%s(详情:%s)", message, ex.getMessage()));
        }
    }

    // 发布主观题异步判分任务并等待结果
    private UserAnswerList processSubjectiveQuestionsByAsyncJudge(BigQuestionGroup group, Map<String, List<UserAnswer>>  answersMap) {
        List<UserAnswer> answers = answersMap.getOrDefault(group.getBizGroupId(), new ArrayList<>());
        UserAnswerList userAnswerList = new UserAnswerList(answers);

        // 主观题处理：启动判题任务并等待结果
        List<JudgeTaskTicket> judgeTaskTickets = userAnswerService.judgeStart(group, userAnswerList);
        if (CollectionUtils.isEmpty(judgeTaskTickets)) {
            throw new BizException(MessageFormat.format("主观题判分任务发布失败, 大题ID:{0}", group.getBizGroupId()));
        }

        // 取得客观题作答结果
        return fetchJudgeResult(group, userAnswerList);
    }

    // 取得主观题异步判分结果（带超时控制）
    private UserAnswerList fetchJudgeResult(BigQuestionGroup group, UserAnswerList userAnswerList) {
        String bizGroupId = group.getBizGroupId();

        // 轮询获取判分结果
        Optional<UserAnswerList> resultOpt = pollForResult(userAnswerList, bizGroupId);
        if (resultOpt.isEmpty()) {
            throw new BizException(MessageFormat.format("主观题判分结果获取超时，大题ID:{0}", bizGroupId));
        }
        log.debug("主观题判分结果获取成功，大题ID:{}", bizGroupId);
        UserAnswerList result = resultOpt.get();
        result.getUserAnswers().forEach(ua->log.debug("主观题判分结果, ID:{},Evaluation:{}", ua.getBizQuestionId(), ua.getEvaluation()));

        // 执行评分处理
        return userAnswerService.judgeScore(group, result);
    }

    // 循环获取判分结果
    private Optional<UserAnswerList> pollForResult(UserAnswerList userAnswerList, String bizGroupId) {

        for (int retry = 0; retry < RETRY_COUNT; retry++) {
            ThreadUtil.sleep(RETRY_INTERVAL);

            // 获取判分结果
            UserAnswerList result = userAnswerService.fetchJudgeResult(userAnswerList);
            if (isValidResult(result)) {
                return Optional.of(result);
            }
            log.debug("等待主观题返回结果，大题ID:{} 尝试次数:{}", bizGroupId, retry + 1);
        }
        return Optional.empty();
    }

    // 验证判分结果是否有效
    private boolean isValidResult(UserAnswerList result) {
        return result != null && !CollectionUtils.isEmpty(result.getUserAnswers()) &&
               result.getUserAnswers().stream().allMatch(ua -> StringUtils.hasText(ua.getEvaluation()));
    }

    /**
     * 保存用户作答记录
     * @param batchId 用户作答提交批次ID（试卷实例ID）
     * @param answersMap 用户作答
     */
    public void saveAnswers(String batchId, Map<String, List<UserAnswer>> answersMap) {
        // 取得当前用户作答记录，并设置作答未通过（用于临时保存）
        List<UserAnswer> currentAnswers = answersMap.values().stream()
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .peek(ua -> ua.setPass(Boolean.FALSE))
                .collect(Collectors.toList());

        // 查询用户历史作答记录
        List<UserAnswer> existingAnswers = userAnswerService.getUserAnswersByBatchId(batchId);

        if (!CollectionUtils.isEmpty(existingAnswers)) {
            Set<String> currentQuestionIds = currentAnswers.stream()
                    .map(UserAnswer::getBizQuestionId)
                    .collect(Collectors.toSet());

            // 查询需要删除的作答记录
            List<UserAnswer> answersToDisable = existingAnswers.stream()
                    .filter(existing -> !currentQuestionIds.contains(existing.getBizQuestionId()))
                    .peek(ua -> ua.setEnable(Boolean.FALSE))
                    .toList();

            // 向新的作答记录中添加需要删除的作答记录
            currentAnswers.addAll(answersToDisable);
        }

        userAnswerService.saveUserAnswers(currentAnswers);
    }

    /**
     * 取得试卷实例中所有题目的用户作答
     * @param paperInstance 试卷实例
     * @return 用户作答
     */
    public Map<String, List<UserAnswer>> getAnswers(PaperInstance paperInstance) {

        Map<String, List<UserAnswer>> answersMap = new HashMap<>();
        String batchId = paperInstance.getInstanceId();

        // 1.根据成绩批次ID查询用户作答记录
        List<UserAnswer> userAnswersByBatchId = userAnswerService.getUserAnswersByBatchId(batchId);
        if(CollectionUtils.isEmpty(userAnswersByBatchId)) {
            return answersMap;
        }
        // 2.生成用户作答Map
        Map<String, UserAnswer> userAnswerMapByBizQuestionId = userAnswersByBatchId.stream()
                .collect(Collectors.toMap(UserAnswer::getBizQuestionId, v -> v, (v1, v2) -> v1));

        // 3.取得所有题目
        List<BigQuestionGroup> bigQuestionGroupList = paperInstance.getBigQuestionGroupList();
        if(CollectionUtils.isEmpty(bigQuestionGroupList)) {
            return answersMap;
        }
        // 4.根据题目ID分组
        bigQuestionGroupList.forEach(bigQuestionGroup -> {
            String bigBizQuestionId = bigQuestionGroup.getBizGroupId();
            // 取得标准答案Map（字题业务ID为主键）
            Map<String, List<QuestionAnswer>> standardAnswerMap = bigQuestionGroup.fetchCorrectAnswers();
            // 设置用户作答Map（字题业务ID为主键）
            standardAnswerMap.keySet().forEach(bizQuestionId -> {
                UserAnswer userAnswer = userAnswerMapByBizQuestionId.get(bizQuestionId);
                if(userAnswer != null) {
                    answersMap.computeIfAbsent(bigBizQuestionId, k -> new ArrayList<>()).add(userAnswer);
                }
            });
        });
        return answersMap;
    }
}
