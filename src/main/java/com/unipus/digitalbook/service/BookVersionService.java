package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.entity.publish.BookVersion;
import com.unipus.digitalbook.model.entity.publish.BookPublishedVersionList;
import com.unipus.digitalbook.model.params.publish.BookPublishedVersionParam;
import jakarta.annotation.Nullable;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 教材版本信息服务接口
 */
public interface BookVersionService {

    /**
     * 根据 教材ID 查询教材最新上架版本信息
     * @param bookId 教材ID
     * @return 教材版本信息
     */
    BookVersion getBookLastPublishedVersion(String bookId);

    /**
     * 根据教材ID和版本号 获取上架版本信息
     *
     * @param bookId     教材ID
     * @param versionNum 版本号
     * @return
     */
    @Nullable
    BookVersion getBookVersionByBookIdAndVersion(String bookId, String versionNum);

    /**
     * 根据 ID 查询教材版本信息
     * @param id 上架版本 ID
     * @return 教材版本信息
     */
    BookVersion getBookVersionById(Long id);

    /**
     * 查询所有教材版本信息
     * @return 教材版本信息列表
     */
    List<BookVersion> getAllBookVersions();

    /**
     * 插入教材版本信息
     * @param bookVersion 教材版本信息
     * @return 返回新增的教材版本 ID
     */
    Long addBookVersion(BookVersion bookVersion);

    /**
     * 根据 ID 更新教材版本信息
     * @param bookVersion 教材版本信息
     * @return 更新是否成功
     */
    boolean updateBookVersion(BookVersion bookVersion);

    /**
     * 根据 ID 删除教材版本信息
     * @param id 上架版本 ID
     * @return 删除是否成功
     */
    boolean deleteBookVersionById(Long id);

    /**
     * 根据条件查询教材所有已上架版版本信息
     *
     * @param param 查询条件
     * @return 教材版本信息
     */
    BookPublishedVersionList getBookPublishedVersion(BookPublishedVersionParam param);

    /**
     * 根据 教材ID 查询教材所有已上架版版本信息
     *
     * @param bookId 教材ID
     * @return 教材版本信息
     */
    List<BookVersion> getBookPublishedVersion(String bookId);

    /**
     * 根据教材ID，获取教材已上架版本的次数
     *
     * @param bookId 教材ID
     * @return 教材已上架版本的次数
     */
    Integer getBookPublishedVersionCount(String bookId);

    /**
     * 根据教材ID列表，获取教材是否有已上架版本
     *
     * @param bookIds 教材ID列表
     * @return 教材是否有已上架版本
     */
    Map<String, Boolean> isBookPublishedVersion(List<String> bookIds);

    Map<String, Long> bookMaxPublishedTime(List<String> bookIds);

    /**
     * 根据版本id查询上一个版本
     *
     * @param id 上架版本 ID
     * @return 教材版本信息
     */
    BookVersion getBookPreviousVersionById(Long id);

    /**
     * 根据版本id，获取教材上架版本顺序
     *
     * @param id 上架版本 ID
     * @return 教材上架版本顺序
     */
    Integer getBookPublishedVersionSortOrder(Long id);

    /**
     * 根据章节版本id查询教材版本信息
     *
     * @param chapterVersionId 章节版本 ID
     * @return 教材版本信息
     */
    BookVersion getBookVersionByChapterVersionId(Long chapterVersionId);

}
