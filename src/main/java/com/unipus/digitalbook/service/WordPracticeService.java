package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.dto.wordpractice.WordPracticeListDTO;
import com.unipus.digitalbook.model.entity.wordpractice.WordPractice;
import com.unipus.digitalbook.model.entity.wordpractice.WordPracticeSearchList;

import java.util.List;

/**
 * 词汇学练服务接口
 *
 * <AUTHOR>
 * @date 2025年06月27日 10:55:16
 */
public interface WordPracticeService {

    /**
     * 词汇学练列表
     *
     * @param offset 偏移量
     * @param limit  每页显示的记录数
     * @return 词汇学练搜索列表
     */
    WordPracticeSearchList searchList(String parentId, int offset, int limit);

    /**
     * 添加词汇学练信息
     *
     * @param wordPractice 词汇学练实体对象，包含需要添加的词汇学练相关信息
     * @return 返回添加操作的结果标识，通常为添加成功后的唯一标识字符串
     */
    String addWordPractice(WordPractice wordPractice);

    /**
     * 编辑词汇学练信息
     *
     * @param wordPractice 词汇学练实体对象，包含需要更新的词汇学练相关信息
     * @return 编辑操作是否成功，成功返回 true，失败返回 false
     */
    boolean editWordPractice(WordPractice wordPractice);

    /**
     * 根据ID获取词汇学练信息
     *
     * @param id 词汇学练信息的唯一标识ID
     * @return 对应的词汇学练实体对象
     */
    WordPractice getEditStatusWordPracticeById(Long id);

    /**
     * 根据ID获取发布状态下的词汇学练信息
     *
     * @param bizId 词汇学练信息的唯一标识ID
     * @return 对应的发布状态下的词汇学练实体对象
     */
    WordPractice getPublishStatusWordPracticeById(String bizId);

    /**
     * 根据多个ID批量删除词汇学练信息
     *
     * @param id 待删除的词汇学练信息的唯一标识ID
     * @return 删除操作操作是否成功，成功返回 true，失败返回 false
     */
    boolean deleteWordPractice(Long id);

    /**
     * 批量发布词汇学练信息
     * @param bookId 书籍ID
     * @param parentIds 上級id集合
     * @param logId 日志id
     * @return 发布操作是否成功，成功返回 true，失败返回 false
     */
    boolean publishBatch(String bookId, List<String> parentIds, Long logId);

    /**
     * 获取书籍所有词汇学练
     * @param bookId 书籍ID
     * @return 词汇学练列表
     */
    WordPracticeListDTO getBookAllWordPractice(String bookId);

    /**
     * 获取词汇学练API服务token
     * @return token
     */
    String generateWordPracticeApiServiceToken();
}
