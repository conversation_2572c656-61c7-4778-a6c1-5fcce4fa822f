package com.unipus.digitalbook.service.remote.restful.knowledge;

import com.unipus.digitalbook.service.remote.restful.knowledge.model.common.*;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.request.*;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.response.*;
import jakarta.validation.constraints.NotNull;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.service.annotation.GetExchange;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

import java.util.List;
import java.util.Map;

/**
 * 图谱创建相关接口
 */
@HttpExchange("/")
public interface KnowledgeApiService {

    /**
     * 新增知识图谱
     *
     * @param params
     * @return knoledgeId
     */
    @NotNull
    @PostExchange(value = "knowledge/add")
    BaseKnowledgeResponse<String> knowledgeAdd(@RequestBody KnowledgeAddRequest params);

    /**
     * 更新知识图谱
     *
     * @param params
     * @return null
     */

    @PostExchange(value = "knowledge/update")
    BaseKnowledgeResponse knowledgeUpdate(@RequestBody KnowledgeUpdateRequest params);

    /**
     * 删除知识图谱
     *
     * @param knowledgeId
     * @return null
     */

    @GetExchange("knowledge/delete")
    BaseKnowledgeResponse knowledgeDelete(@RequestParam("knowledgeId") String knowledgeId);

    /**
     * 获取知识图谱
     *
     * @param knowledgeId
     * @return null
     */

    @GetExchange("knowledge/detail")
    BaseKnowledgeResponse<Knowledge> knowledgeGet(@RequestParam("knowledgeId") String knowledgeId);

    /**
     * 知识图谱上线
     *
     * @param request
     * @return
     */

    @PostExchange("knowledge/publish")
    BaseKnowledgeResponse knowledgePublish(@RequestBody KnowledgePublishRequest request);


    /**
     * 知识图谱文件上传
     *
     * @param
     * @return fileUrl
     */

    @PostExchange(url = "knowledge/file/upload", contentType = MediaType.MULTIPART_FORM_DATA_VALUE)
    BaseKnowledgeResponse<String> knowledgeFileUpload(@RequestPart("file") MultipartFile file);

    /**
     * 知识图谱列表
     *
     * @param
     * @return fileUrl
     */

    @GetExchange(url = "knowledge/list")
    BaseKnowledgeResponse<List<Knowledge>> knowledgeList(@RequestParam("keyword") String keyword);

    /**
     * 联想词搜索 近似搜索
     *
     * @param
     * @return fileUrl
     */

    @GetExchange(url = "knowledge/query")
    BaseKnowledgeResponse<List<Knowledge>> knowledgeQuery(@RequestParam String query);

    /*---------------------------------子图内容------------------------------------*/


    /**
     * 子图详细内容
     * @param graphId
     * @return
     */
    @GetExchange(url = "knowledge/graph/detail")
    BaseKnowledgeResponse<GraphNodeInfoResponse> knowledgeGraphNodeList(@RequestParam String graphId);


    /*---------------------------------知识点级-------------------------------------*/

    /**
     * 新增根节点
     *
     * @param params
     * @return knoledgeId
     */
    @NotNull
    @PostExchange(value = "knowledge/node/simple/add")
    BaseKnowledgeResponse<KnowledgeNodeAddResponse> knowledgeNodeAdd(@RequestBody KnowledgeNodeBaseAddRequest params);

    /**
     * 更新根节点
     *
     * @param params
     * @return null
     */
    @PostExchange(value = "knowledge/node/simple/update")
    BaseKnowledgeResponse knowledgeNodeSimpleUpdate(@RequestBody KnowledgeNodeUpdateRequest params);


    /**
     * 新增同级知识点
     *
     * @param params
     * @return
     */
    @PostExchange(value = "knowledge/node/sameLevel/add")
    BaseKnowledgeResponse<String> knowledgeNodeSameLevelAdd(@RequestBody KnowledgeNode params);


    /**
     * 新增子级知识点
     *
     * @param params 知识图谱节点参数
     * @return 知识图谱节点ID
     */
    @PostExchange(value = "knowledge/node/subLevel/add")
    BaseKnowledgeResponse<String> knowledgeNodeSubLevelAdd(@RequestBody KnowledgeNode params);

    /**
     * 更新知识点
     *
     * @param params 知识图谱节点参数
     * @return 更新结果
     */
    @PostExchange(value = "knowledge/node/update")
    BaseKnowledgeResponse knowledgeNodeUpdate(@RequestBody KnowledgeNode params);

    /**
     * 移动知识点
     *
     * @param params 知识图谱节点参数
     * @return 移动结果
     */
    @PostExchange(value = "knowledge/node/move")
    BaseKnowledgeResponse<String> knowledgeNodeMove(@RequestBody KnowledgeNodeMoveRequest params);

    /**
     * 删除知识点
     *
     * @param params 知识图谱节点ID
     * @return 删除结果
     */
    @PostExchange(value = "knowledge/node/delete")
    BaseKnowledgeResponse knowledgeNodeDelete(@RequestBody KnowledgeNodeDeleteRequest params);

    /**
     * 查看知识点
     *
     * @param nodeId 知识图谱节点ID
     * @return 知识图谱节点信息
     */
    @GetExchange(value = "knowledge/node/detail")
    BaseKnowledgeResponse<KnowledgeNodeDetailResponse> knowledgeNodeDetail(@RequestParam String nodeId);

    /**
     * 联想词搜索知识点
     *
     * @param query   关键词
     * @param graphId 子图 id
     * @return 知识图谱节点列表
     */
    @GetExchange(value = "knowledge/node/query")
    BaseKnowledgeResponse<String> knowledgeNodeQuery(@RequestParam String query, @RequestParam String graphId);

    /**
     * 同级移动知识点
     *
     * @param params 知识图谱节点参数
     * @return 移动结果
     */
    @PostExchange(value = "knowledge/node/change")
    BaseKnowledgeResponse knowledgeNodeChange(@RequestBody String params);

    /**
     * 更新知识点位置
     *
     * @param params 知识图谱节点参数
     * @return 更新结果
     */
    @PostExchange(value = "knowledge/node/styles/update")
    BaseKnowledgeResponse knowledgeNodeStylesUpdate(@RequestBody String params);


    /*==============================关系============================*/

    /**
     * 新增关系
     *
     * @param params 新增关系请求参数
     * @return 基础响应
     */
    @PostExchange(value = "knowledge/relation/add")
    BaseKnowledgeResponse<String> relationAdd(@RequestBody KnowledgeRelationAddRequest params);

    /**
     * 更新关系
     *
     * @param params 更新关系请求参数
     * @return 基础响应
     */
    @PostExchange(value = "knowledge/relation/update")
    BaseKnowledgeResponse relationUpdate(@RequestBody KnowledgeRelationUpdateRequest params);

    /**
     * 删除关系
     *
     * @param relationId 关系ID
     * @return 基础响应
     */
    @GetExchange(value = "knowledge/relation/delete")
    BaseKnowledgeResponse relationDelete(@RequestParam String relationId);

    /**
     * 搜索关系类型
     *
     * @param query 搜索条件
     * @return 关系类型列表响应
     */
    @GetExchange(value = "knowledge/relation/query")
    BaseKnowledgeResponse<KnowledgeRelationQueryResponse> relationQuery(@RequestParam String query, @RequestParam String graphId);

    /*==================阅读端=========================*/

    /**
     * 查询知识图谱列表
     *
     * @param keyword 搜索条件
     * @return 知识图谱列表响应
     */
    @GetExchange(value = "/version/list")
    BaseKnowledgeResponse<List<Knowledge>> knowledgeGraphList(@RequestParam String keyword);

    /**
     * 联想搜索知识图谱
     *
     * @param query 搜索条件
     * @return 知识图谱搜索响应
     */
    @GetExchange(value = "/version/query")
    BaseKnowledgeResponse<List<Knowledge>> knowledgeGraphSearch(@RequestParam(value = "query") String query);

    /*========================资源标签管理==========================*/

    /**
     * 列表展示标签类型
     *
     * @return 标签类型列表响应
     */
    @GetExchange(value = "/label/list")
    BaseKnowledgeResponse<PaginationResponse> labelList(@RequestParam String keyword, @RequestParam Integer pageNum, @RequestParam Integer pageSize);

    /**
     * 新增标签类型
     *
     * @param params 新增标签类型请求参数
     * @return 基础响应
     */
    @PostExchange(value = "/label/add")
    BaseKnowledgeResponse<ResourceTag> labelAdd(@RequestBody ResourceTag params);

    /**
     * 更新标签类型
     *
     * @param params 更新标签类型请求参数
     * @return 基础响应
     */
    @PostExchange(value = "/label/update")
    BaseKnowledgeResponse<ResourceTag> labelUpdate(@RequestBody ResourceTag params);

    /**
     * 删除标签类型
     *
     * @param labelId 标签类型ID
     * @return 基础响应
     */
    @GetExchange(value = "/label/delete")
    BaseKnowledgeResponse labelDelete(@RequestParam String labelId);



    /**
     * 查询知识标签
     *
     * @param knowledgeId 节点ID
     * @return 基础响应
     */
    @GetExchange(value = "resource/nodes/detail")
    BaseKnowledgeResponse<List<KnowledgeResourceNodeResponse>> getKnowledgeResourceNodeDetail(@RequestParam("knowledgeId") String knowledgeId);

    /**
     * 查询资源标签
     *
     * @return 基础响应
     */
    @GetExchange(value = "resource/labels/detail")
    BaseKnowledgeResponse<List<ResourceLabelResponse>> getResourceLabelDetail();

    /**
     * 查询资源
     *
     * @param resourceId 资源ID
     * @return 基础响应
     */
    @GetExchange(value = "resource/detail")
    BaseKnowledgeResponse<KnowledgeResourceDetailResponse> getResourceDetail(@RequestParam String resourceId);

    /**
     * 批量查询资源
     *
     * @param request 资源ID列表
     * @return 基础响应
     */
    @PostExchange(value = "resource/list/detail")
    BaseKnowledgeResponse<Map<String, KnowledgeResourceDetailResponse>> getResourceListDetail(@RequestBody KnowledgeResourceListDetailRequest request);

    /**
     * 新增资源
     *
     * @param request 新增资源请求参数
     * @return 基础响应
     */
    @PostExchange(value = "resource/add")
    BaseKnowledgeResponse<String> addResource(@RequestBody KnowledgeResourceAddRequest request);

    /**
     * 更新资源
     *
     * @param resource 更新资源请求参数
     * @return 基础响应
     */
    @PostExchange(value = "resource/update")
    BaseKnowledgeResponse updateResource(@RequestBody KnowledgeResourceUpdateRequest resource);

    /**
     * 删除资源
     *
     * @param id 资源ID
     * @param knowledgeId 图谱Id
     * @return 基础响应
     */
    @GetExchange(value = "resource/delete")
    BaseKnowledgeResponse deleteResource(@RequestParam String id,@RequestParam String knowledgeId);

    /**
     * 删除资源
     *
     * @param request 待删除的资源Ids
     * @return 基础响应
     */
    @PostExchange(value = "resource/list/delete")
    BaseKnowledgeResponse batchDeleteResource(@RequestBody KnowledgeResourceBatchDelRequest request);





}
