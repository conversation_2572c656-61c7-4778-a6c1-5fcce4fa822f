package com.unipus.digitalbook.service.processor.tenant;

import com.unipus.digitalbook.model.enums.ChannelEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class MessageProcessorFactory {

    private final Map<ChannelEnum, MessageProcessor> factories = new HashMap<>();

    @Autowired
    public MessageProcessorFactory(ApplicationContext applicationContext) {
        Map<String, MessageProcessor> processorBeans =
                applicationContext.getBeansOfType(MessageProcessor.class);
        for (Map.Entry<String, MessageProcessor> entry : processorBeans.entrySet()) {
            MessageProcessor processor = entry.getValue();
            factories.put(processor.supportChannel(), processor);
        }
    }

    public MessageProcessor getProcessor(ChannelEnum channel) {
        return factories.get(channel);
    }
}
