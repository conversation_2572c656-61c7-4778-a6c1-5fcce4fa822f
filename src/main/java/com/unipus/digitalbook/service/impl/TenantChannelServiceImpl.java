package com.unipus.digitalbook.service.impl;

import com.github.benmanes.caffeine.cache.LoadingCache;
import com.unipus.digitalbook.dao.TenantChannelPOMapper;
import com.unipus.digitalbook.model.entity.tenant.TenantSubscribeID;
import com.unipus.digitalbook.model.po.tenant.TenantChannelPO;
import com.unipus.digitalbook.service.TenantChannelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class TenantChannelServiceImpl implements TenantChannelService {

    private final TenantChannelPOMapper tenantChannelPOMapper;

    private final  LoadingCache<TenantSubscribeID, Map<Integer, TenantChannelPO>> tenantChannelCache;

    @Autowired
    public TenantChannelServiceImpl(TenantChannelPOMapper tenantChannelPOMapper, LoadingCache<TenantSubscribeID, Map<Integer, TenantChannelPO>> tenantChannelCache) {
        this.tenantChannelPOMapper = tenantChannelPOMapper;
        this.tenantChannelCache = tenantChannelCache;
    }

    @Override
    public Map<Integer, TenantChannelPO> selectByTenantIdAndMessageTopic(Long tenantId, String messageTopic) {
        return tenantChannelCache.get(new TenantSubscribeID(tenantId, messageTopic));
    }
}
