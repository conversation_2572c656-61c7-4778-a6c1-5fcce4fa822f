package com.unipus.digitalbook.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.dao.OrgRoleRelationPOMapper;
import com.unipus.digitalbook.dao.RolePOMapper;
import com.unipus.digitalbook.dao.RolePermissionRelationPOMapper;
import com.unipus.digitalbook.dao.RoleUserRelationPOMapper;
import com.unipus.digitalbook.model.constants.CacheConstant;
import com.unipus.digitalbook.model.dto.ApiDTO;
import com.unipus.digitalbook.model.dto.ApiListDTO;
import com.unipus.digitalbook.model.dto.ControllerPermissionListDTO;
import com.unipus.digitalbook.model.entity.Organization;
import com.unipus.digitalbook.model.entity.role.Role;
import com.unipus.digitalbook.model.entity.role.RolePermission;
import com.unipus.digitalbook.model.entity.role.RoleSearchList;
import com.unipus.digitalbook.model.entity.role.RoleUser;
import com.unipus.digitalbook.model.enums.RolePermissionTypeEnum;
import com.unipus.digitalbook.model.enums.StatusEnum;
import com.unipus.digitalbook.model.po.role.OrgRoleRelationPO;
import com.unipus.digitalbook.model.po.role.RolePO;
import com.unipus.digitalbook.model.po.role.RolePermissionRelationPO;
import com.unipus.digitalbook.model.po.role.RoleUserRelationPO;
import com.unipus.digitalbook.service.InterfaceService;
import com.unipus.digitalbook.service.OrgService;
import com.unipus.digitalbook.service.RoleService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 业务逻辑层实现类，处理角色相关的操作。
 */
@Service
@Slf4j
public class RoleServiceImpl implements RoleService {

    @Resource
    private RolePOMapper rolePOMapper;
    @Resource
    private OrgRoleRelationPOMapper orgRoleRelationPOMapper;

    @Resource
    private RolePermissionRelationPOMapper rolePermissionRelationPOMapper;

    @Resource
    private InterfaceService interfaceService;

    @Resource
    private RoleUserRelationPOMapper roleUserRelationPOMapper;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private OrgService orgService;

    /**
     * 保存角色（新增或更新）
     *
     * @param role 业务逻辑层角色对象
     * @return 是否保存成功
     */
    @Override
    @Transactional
    public Long save(Role role) {
        RolePO rolePO = new RolePO(role);
        Long roleId = role.getId();
        List<RolePO> rolePOS = rolePOMapper.selectByName(rolePO.getName());
        if (!CollectionUtils.isEmpty(rolePOS)) {
            if (roleId == null || roleId <= 0) {
                throw new IllegalArgumentException("角色名称重复");
            }
            if (rolePOS.stream().anyMatch(r -> !r.getId().equals(roleId))) {
                throw new IllegalArgumentException("角色名称重复");
            }
        }
        if (roleId == null || roleId <= 0) {
            rolePOMapper.insertSelective(rolePO);
        } else {
            rolePOMapper.updateByPrimaryKeySelective(rolePO);
        }
        return rolePO.getId();
    }

    /**
     * 为机构分配角色
     *
     * @param roleIds  角色ID 如果角色id是空则删除组织下所有的id
     * @param orgId    组织ID
     * @param opUserId 操作人ID
     * @return 是否保存成功
     */
    @Override
    public boolean bathAssignRole2Org(List<Long> roleIds, Long orgId, Long opUserId) {
        if (orgId == null || orgId <= 0) {
            throw new IllegalArgumentException("机构ID不能为空");
        }
        if (CollectionUtils.isEmpty(roleIds)) {
            throw new IllegalArgumentException("角色不能为空");
        }
        // 获取机构下所有角色
        Set<Long> orgAllRoleIds = new HashSet<>(orgRoleRelationPOMapper.selectRoleIdByOrgId(orgId));
        List<OrgRoleRelationPO> orgRoleRelations = roleIds.stream()
                // 移除已存在的角色, 如果存在remove返回true，剩余的allRoleIds是需要删除的角色
                .filter(roleId -> !orgAllRoleIds.remove(roleId))
                .map(roleId ->
                        new OrgRoleRelationPO(orgId, roleId, opUserId, true))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(orgAllRoleIds)) {
            for (Long removeRoleId : orgAllRoleIds) {
                orgRoleRelations.add(new OrgRoleRelationPO(orgId, removeRoleId, opUserId, false));
            }
        }
        if (!CollectionUtils.isEmpty(orgRoleRelations)) {
            orgRoleRelationPOMapper.batchInsertOrUpdate(orgRoleRelations);
        }
        return true;
    }

    @Override
    @Transactional
    public boolean delete(Long userId, Long roleId) {
        RolePO rolePO = rolePOMapper.selectByPrimaryKey(roleId);
        if (rolePO == null) {
            throw new IllegalArgumentException("角色不存在");
        }
        if (StatusEnum.isEnable(rolePO.getStatus())) {
            throw new IllegalArgumentException("角色未禁用，无法删除");
        }
        rolePOMapper.logicalDelete(userId, roleId);
        // 删除机构角色关联
        List<Long> relationIds = orgRoleRelationPOMapper.selectIdByRoleId(roleId);
        if (!CollectionUtils.isEmpty(relationIds)) {
            orgRoleRelationPOMapper.bathLogicalDelete(userId, relationIds);
        }
        // 删除用户关联的角色
        List<Long> userRelationIds = roleUserRelationPOMapper.selectIdByRoleId(roleId);
        if (!CollectionUtils.isEmpty(userRelationIds)) {
            roleUserRelationPOMapper.bathLogicalDelete(userId, userRelationIds);
        }
        return true;
    }

    @Override
    @Transactional
    public boolean updateStatus(Long opUserId, Long id, Integer status) {
        return rolePOMapper.updateStatus(opUserId, id, status) > 0;
    }

    @Override
    public Role getRoleById(Long id) {
        RolePO rolePO = rolePOMapper.selectByPrimaryKey(id);
        if (rolePO == null) {
            return null;
        }
        return rolePO.toRole();
    }

    @Override
    public List<Role> orgAssignedRoleList(Long orgId) {
        if (orgId == null || orgId <= 0) {
            return Collections.emptyList();
        }
        List<Long> roleIds = orgRoleRelationPOMapper.selectRoleIdByOrgId(orgId);
        if (CollectionUtils.isEmpty(roleIds)) {
            return Collections.emptyList();
        }
        return rolePOMapper.selectByIds(roleIds)
                .stream().filter(r -> StatusEnum.ENABLE.getCode().equals(r.getStatus()))
                .map(RolePO::toRole).sorted(Comparator.comparing(Role::getCreateTime).reversed()).toList();
    }

    @Override
    public List<Role> userAssignedRoleList(Long userId, Long orgId) {
        if (userId == null || userId <= 0 || orgId == null || orgId <= 0) {
            return Collections.emptyList();
        }
        Organization orgDetail = orgService.getOrgDetail(orgId);
        if (orgDetail == null) {
            return Collections.emptyList();
        }
        if (!StatusEnum.ENABLE.getCode().equals(orgDetail.getStatus())) {
            return Collections.emptyList();
        }
        Set<Long> orgRoleIds = new HashSet<>(orgRoleRelationPOMapper.selectRoleIdByOrgId(orgId));
        List<RoleUserRelationPO> roleUserRelationPOS = roleUserRelationPOMapper.selectByUserIdAndOrgId(userId, orgId);
        if (CollectionUtils.isEmpty(roleUserRelationPOS)) {
            return Collections.emptyList();
        }
        List<Long> roleIds = roleUserRelationPOS.stream().map(RoleUserRelationPO::getRoleId).filter(orgRoleIds::contains).toList();
        if (CollectionUtils.isEmpty(roleIds)) {
            return Collections.emptyList();
        }
        return rolePOMapper.selectByIds(roleIds)
                .stream().filter(r -> StatusEnum.ENABLE.getCode().equals(r.getStatus()))
                .map(RolePO::toRole).sorted(Comparator.comparing(Role::getCreateTime).reversed()).toList();
    }

    @Override
    public RoleSearchList search(String name, Integer status, int offset, int limit) {
        List<RolePO> search = rolePOMapper.search(name, status, offset, limit);
        Long count = rolePOMapper.count(name, status, offset, limit);
        RoleSearchList roleSearch = new RoleSearchList();
        roleSearch.setRoles(search.stream().map(RolePO::toRole).toList());
        roleSearch.setTotalCount(count.intValue());
        return roleSearch;
    }

    @Override
    public boolean assignRolePermission(RolePermission rolePermission, RolePermissionTypeEnum type, Long opUserId) {
        if (rolePermission == null || CollectionUtils.isEmpty(rolePermission.getPermissionItems())) {
            return false;
        }
        Long roleId = rolePermission.getRoleId();
        if (roleId == null || roleId <= 0) {
            return false;
        }
        List<RolePermissionRelationPO> rolePermissionRelations = rolePermission.getPermissionItems().stream().map(permissionParam -> {
            RolePermissionRelationPO relationPO = new RolePermissionRelationPO();
            relationPO.setRoleId(roleId);
            relationPO.setPermissionReference(permissionParam.getPermissionReference());
            relationPO.setType(type.getCode());
            relationPO.setEnable(permissionParam.isEnable());
            relationPO.setCreateBy(opUserId);
            relationPO.setUpdateBy(opUserId);
            return relationPO;
        }).collect(Collectors.toList());
        rolePermissionRelationPOMapper.batchInsertOrUpdate(rolePermissionRelations);
        return true;
    }

    @Override
    public ApiListDTO getInterfaceList(List<Long> roleIds) {
        ControllerPermissionListDTO allPermissions = interfaceService.getAllPermissions();
        if (CollectionUtils.isEmpty(roleIds)) {
            return new ApiListDTO(allPermissions.getControllerResourceList());
        }
        List<RolePermissionRelationPO> rolePermissionRelations =
                rolePermissionRelationPOMapper.selectByRoleIdsAndType(roleIds, RolePermissionTypeEnum.INTERFACE.getCode());

        if (CollectionUtils.isEmpty(rolePermissionRelations)) {
            return new ApiListDTO(allPermissions.getControllerResourceList());
        }
        List<ApiDTO> apiList = new ArrayList<>();
        Map<String, RolePermissionRelationPO> assignPermissions = rolePermissionRelations.stream()
                .collect(Collectors.toMap(RolePermissionRelationPO::getPermissionReference, r -> r, (r1, r2) -> r1));
        allPermissions.getControllerResourceList().forEach(controllerPermissionDTO ->
                controllerPermissionDTO.getResourceList().forEach(permission -> {
                    RolePermissionRelationPO rolePermission = assignPermissions.get(permission.getApiUrl());
                    if (rolePermission != null) {
                        permission.setAssigned(rolePermission.getEnable());
                        permission.setAssignedTime(rolePermission.getCreateTime().getTime());
                    }
                    apiList.add(new ApiDTO(permission, controllerPermissionDTO));
                }));
        apiList.sort(Comparator.comparing(ApiDTO::getAssignedTime, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
        ApiListDTO apiListDTO = new ApiListDTO();
        apiListDTO.setApiList(apiList);
        return apiListDTO;
    }

    @Override
    public Set<String> getAssignedPermissions(List<Long> roleIds, RolePermissionTypeEnum type) {
        List<RolePermissionRelationPO> rolePermissionRelations = rolePermissionRelationPOMapper.selectByRoleIdsAndType(roleIds, type.getCode());
        if (CollectionUtils.isEmpty(rolePermissionRelations)) {
            return Collections.emptySet();
        }
        return rolePermissionRelations.stream().map(RolePermissionRelationPO::getPermissionReference).collect(Collectors.toSet());
    }

    /**
     * 根据用户id取得用户组织角色关系
     *
     * @param userId 用户id
     * @return List<RoleUserRelationPO>
     */
    @Override
    public List<RoleUserRelationPO> getByUserId(Long userId) {
        List<RoleUserRelationPO> roleUserRelation = roleUserRelationPOMapper.selectRoleMapByUserId(userId);
        if (CollectionUtils.isEmpty(roleUserRelation)) {
            return Collections.emptyList();
        }
        return roleUserRelation;
    }

    @Override
    public boolean bathAssignRole2User(RoleUser roleUser, Long opUserId) {
        if (roleUser.getUserId() == null || roleUser.getUserId() <= 0) {
            throw new IllegalArgumentException("userId不能为空");
        }
        Long userId = roleUser.getUserId();
        Long orgId = roleUser.getOrgId();
        if (orgId == null || orgId <= 0) {
            throw new IllegalArgumentException("orgId不能为空");
        }
        // 查询用户在组织中的角色ID集合
        Set<Long> userAllRoleIds = roleUserRelationPOMapper
                .selectByUserIdAndOrgId(userId, orgId)
                .stream()
                .map(RoleUserRelationPO::getRoleId)
                .collect(Collectors.toSet());
        List<RoleUserRelationPO> roleUserRelations = new ArrayList<>();
        List<Long> roleIds = roleUser.getRoleIds();
        if (!CollectionUtils.isEmpty(roleIds)) {
            for (Long roleId : roleUser.getRoleIds()) {
                // 如果该角色不在用户角色集合中，则添加
                if(!userAllRoleIds.remove(roleId)) {
                    RoleUserRelationPO newRelation = new RoleUserRelationPO(userId, roleId, orgId, true, opUserId);
                    roleUserRelations.add(newRelation);
                }
            }
        }
        if (!userAllRoleIds.isEmpty()) {
            // 需要删除的用户角色
            userAllRoleIds.forEach(roleId -> {
                RoleUserRelationPO removeRelation = new RoleUserRelationPO(userId, roleId, orgId, false, opUserId);
                roleUserRelations.add(removeRelation);
            });
        }
        if (roleUserRelations.isEmpty()) {
            return true;
        }
        roleUserRelationPOMapper.batchInsertOrUpdate(roleUserRelations);
        return true;
    }

    /**
     * 根据组织ID获取角色列表
     *
     * @param orgId 组织ID
     * @return 角色列表
     */
    @Override
    public List<Role> getByOrgId(Long orgId) {
        if (orgId == null || orgId <= 0) {
            return Collections.emptyList();
        }
        List<Long> roleIds = orgRoleRelationPOMapper.selectRoleIdByOrgId(orgId);
        if (CollectionUtils.isEmpty(roleIds)) {
            return Collections.emptyList();
        }
        return rolePOMapper.selectByIds(roleIds)
                .stream().map(RolePO::toRole).sorted(Comparator.comparing(Role::getCreateTime).reversed()).toList();
    }

    /**
     * 获取角色权限列表
     *
     * @return 角色权限映射
     */
    @Override
    public Map<Long, Set<String>> getAllPermissions() {
        String redisKey = CacheConstant.REDIS_API_PERMISSION_KEY;

        // 从缓存中获取接口权限
        String permissionsMapStr = stringRedisTemplate.opsForValue().get(redisKey);
        if (StringUtils.hasText(permissionsMapStr)) {
            return JSON.parseObject(permissionsMapStr, new TypeReference<>() {
            });
        }

        // 查询角色接口权限列表
        List<RolePermissionRelationPO> permissions = rolePermissionRelationPOMapper
                .selectByType(RolePermissionTypeEnum.INTERFACE.getCode());
        if (CollectionUtils.isEmpty(permissions)) {
            return Collections.emptyMap();
        }

        // 按角色把接口分组保存
        Map<Long, Set<String>> permissionMap = permissions.stream().collect(Collectors.groupingBy(
                RolePermissionRelationPO::getRoleId,
                Collectors.mapping(RolePermissionRelationPO::getPermissionReference, Collectors.toSet())));
        // 按角色把接口权限保存到用户缓存中
        stringRedisTemplate.opsForValue().set(redisKey, JsonUtil.toJsonString(permissionMap),
                CacheConstant.REDIS_USER_TIMEOUT_SECONDS, TimeUnit.SECONDS);

        // 构建返回结果
        return permissionMap;
    }


}