package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.common.exception.assistant.AssistantException;
import com.unipus.digitalbook.common.utils.IdentifierUtil;
import com.unipus.digitalbook.dao.BookSettingLogPOMapper;
import com.unipus.digitalbook.dao.BookSettingPOMapper;
import com.unipus.digitalbook.model.common.PageParams;
import com.unipus.digitalbook.model.entity.book.BookSettingLog;
import com.unipus.digitalbook.model.entity.book.BookSettingLogList;
import com.unipus.digitalbook.model.params.book.BookSettingLogParam;
import com.unipus.digitalbook.model.params.book.BookSettingParam;
import com.unipus.digitalbook.model.po.book.BookSettingLogPO;
import com.unipus.digitalbook.model.po.book.BookSettingPO;
import com.unipus.digitalbook.service.AssistantService;
import com.unipus.digitalbook.service.BookSettingService;
import com.unipus.digitalbook.service.KnowledgeService;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.common.KnowledgePrimaryIdRequest;
import com.unipus.digitalbook.service.remote.restful.ucontent.BaseResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BookSettingServiceImpl implements BookSettingService {

    @Resource
    private BookSettingPOMapper bookSettingPOMapper;

    @Resource
    private BookSettingLogPOMapper bookSettingLogPOMapper;

    @Resource
    private AssistantService assistantService;

    @Resource
    private KnowledgeService knowledgeService;

    @Override
    public Long upsertSetting(BookSettingParam param, Long currentUserId) {
        String bookId = param.getBookId();
        boolean assistantSetted = param.getIsAssistantSetting();
        boolean assistantOpened = Optional.ofNullable(param.getAssistantOpened()).orElse(false);
        boolean assistantUpdated = Optional.ofNullable(param.getAssistantUpdated()).orElse(false);
        boolean knowledgeUpdated = param.getKnowledgeUpdated();

        AtomicBoolean knowledgeUpdateSuccess = new AtomicBoolean(false);
        AtomicBoolean assistantUpdateSuccess = new AtomicBoolean(false);

        Long result = null;

        try (var executor = Executors.newVirtualThreadPerTaskExecutor()) {
            // 尝试远程调用知识图谱服务发布知识图谱
            CompletableFuture<Void> knowledgeFuture = CompletableFuture.runAsync(() -> {
                if (knowledgeUpdated) {
                    KnowledgePrimaryIdRequest request = new KnowledgePrimaryIdRequest();
                    request.setKnowledgeId(param.getKnowledgeId());
                    request.setId(param.getId());
                    request.setDescription("发布知识图谱");
                    knowledgeService.knowledgePublish(request, currentUserId);
                    knowledgeUpdateSuccess.set(true);
                }
            }, executor);

            // 尝试远程调用数字人服务发布数字人
            CompletableFuture<Void> assistantFuture = CompletableFuture.runAsync(() -> {
                if (assistantSetted && assistantUpdated) {
                    BaseResponse<Void> response = assistantService.publishAll(currentUserId, bookId);
                    if (response.getCode() != 0) {
                        log.error("教材id：{}发布数字人失败，错误码：{}, 错误信息：{}", bookId, response.getCode(), response.getMessage());
                        throw new AssistantException(response.getMessage());
                    }
                    assistantUpdateSuccess.set(true);
                }
            }, executor);

            CompletableFuture.allOf(knowledgeFuture, assistantFuture).join();
        } finally {
            Date now = Calendar.getInstance().getTime();
            String version = IdentifierUtil.getShortUUID();

            // 保存数字人开关设置
            if (assistantSetted) {
                BookSettingPO bookSettingPO = new BookSettingPO();
                bookSettingPO.setBookId(bookId);
                bookSettingPO.setAssistantOpened(assistantOpened);
                bookSettingPO.setCreateTime(now);
                bookSettingPO.setUpdateTime(now);
                bookSettingPO.setCreateBy(currentUserId);
                bookSettingPO.setUpdateBy(currentUserId);
                bookSettingPO.setEnable(true);
                if (assistantUpdateSuccess.get()) {
                    bookSettingPO.setAssistantVersion(version);
                    assistantService.publishByBookId(bookId, version);
                }
                bookSettingPOMapper.upsertAssistantOpened(bookSettingPO);
            }

            // 保存发布日志
            if (assistantSetted || knowledgeUpdateSuccess.get()) {
                BookSettingLogPO bookSettingLogPO = new BookSettingLogPO();
                bookSettingLogPO.setBookId(bookId);
                if (assistantSetted) {
                    bookSettingLogPO.setAssistantOpened(assistantOpened);
                    bookSettingLogPO.setAssistantUpdated(assistantUpdateSuccess.get());
                }
                bookSettingLogPO.setKnowledgeUpdated(knowledgeUpdateSuccess.get());
                bookSettingLogPO.setCreateTime(now);
                bookSettingLogPO.setUpdateTime(now);
                bookSettingLogPO.setCreateBy(currentUserId);
                bookSettingLogPO.setUpdateBy(currentUserId);
                bookSettingLogPO.setEnable(true);

                // 生成数字人版本
                if (assistantUpdateSuccess.get()) {
                    bookSettingLogPO.setAssistantVersion(version);
                }
                bookSettingLogPOMapper.saveLog(bookSettingLogPO);
                result = bookSettingLogPO.getId();
            }
        }

        return result;
    }

    @Override
    public void upsertGreeting(String bookId, String greeting, Long currentUserId) {
        Date now = Calendar.getInstance().getTime();
        BookSettingPO bookSettingPO = new BookSettingPO();
        bookSettingPO.setBookId(bookId);
        bookSettingPO.setGreeting(greeting);
        bookSettingPO.setCreateTime(now);
        bookSettingPO.setUpdateTime(now);
        bookSettingPO.setCreateBy(currentUserId);
        bookSettingPO.setUpdateBy(currentUserId);
        bookSettingPO.setEnable(true);
        bookSettingPOMapper.upsertGreeting(bookSettingPO);
    }

    @Override
    public String greeting(String bookId) {
        BookSettingPO bookSettingPO = bookSettingPOMapper.selectByBookId(bookId);
        if (bookSettingPO == null) {
            return "";
        }
        return Optional.ofNullable(bookSettingPO.getGreeting()).orElse("");
    }

    @Override
    public BookSettingPO selectByBookId(String bookId) {
        return bookSettingPOMapper.selectByBookId(bookId);
    }

    @Override
    public BookSettingLogList searchLog(BookSettingLogParam param) {
        String bookId = param.getBookId();
        String name = param.getName();
        Date beginTime = Optional.ofNullable(param.getBeginTime()).map(Date::new).orElse(null);
        Date endTime = Optional.ofNullable(param.getEndTime()).map(Date::new).orElse(null);
        PageParams pageParams = param.getPageParams();
        List<BookSettingLog> list = bookSettingLogPOMapper.searchLog(bookId, name, beginTime, endTime, pageParams);
        Integer total = bookSettingLogPOMapper.searchLogCount(bookId, name, beginTime, endTime);
        return new BookSettingLogList(list, total);
    }

    @Override
    public Map<String, BookSettingPO> selectByBookIds(List<String> bookIds) {
        return bookSettingPOMapper.selectByBookIds(bookIds)
                .stream().collect(Collectors.toMap(BookSettingPO::getBookId,
                        bookSettingPO -> bookSettingPO,
                        (existing, replacement) -> existing));
    }

}
