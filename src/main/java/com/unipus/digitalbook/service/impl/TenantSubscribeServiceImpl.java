package com.unipus.digitalbook.service.impl;

import com.github.benmanes.caffeine.cache.LoadingCache;
import com.unipus.digitalbook.dao.TenantSubscribePOMapper;
import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.entity.question.QuestionId;
import com.unipus.digitalbook.model.entity.tenant.TenantSubscribeID;
import com.unipus.digitalbook.model.po.tenant.TenantSubscribePO;
import com.unipus.digitalbook.service.TenantSubscribeService;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TenantSubscribeServiceImpl implements TenantSubscribeService {

    private final TenantSubscribePOMapper tenantSubscribePOMapper;

    private final LoadingCache<TenantSubscribeID, TenantSubscribePO> tenantSubscribeCache;

    @Autowired
    public TenantSubscribeServiceImpl(TenantSubscribePOMapper tenantSubscribePOMapper, LoadingCache<TenantSubscribeID, TenantSubscribePO> tenantSubscribeCache) {
        this.tenantSubscribePOMapper = tenantSubscribePOMapper;
        this.tenantSubscribeCache = tenantSubscribeCache;
    }

    @Override
    public TenantSubscribePO selectByTenantIdAndMessageTopic(Long tenantId, String messageTopic) {
       return tenantSubscribeCache.get(new TenantSubscribeID(tenantId, messageTopic));
    }

    @Override
    public List<TenantSubscribePO> selectAll() {
        return tenantSubscribePOMapper.selectAll();
    }
}
