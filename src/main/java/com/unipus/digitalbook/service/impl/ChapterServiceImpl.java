package com.unipus.digitalbook.service.impl;

import com.alibaba.fastjson2.JSON;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.unipus.digitalbook.common.exception.book.ChapterCreationException;
import com.unipus.digitalbook.dao.*;
import com.unipus.digitalbook.model.common.PageParams;
import com.unipus.digitalbook.model.constants.CacheConstant;
import com.unipus.digitalbook.model.entity.UserInfo;
import com.unipus.digitalbook.model.entity.chapter.*;
import com.unipus.digitalbook.model.entity.paper.Paper;
import com.unipus.digitalbook.model.entity.paper.PaperReference;
import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.enums.BookOperationEnum;
import com.unipus.digitalbook.model.enums.EventTypeEnum;
import com.unipus.digitalbook.model.params.book.BookCollaboratorParam;
import com.unipus.digitalbook.model.po.BookVersionChapterVersionRelationPO;
import com.unipus.digitalbook.model.po.book.ChapterQuestionGroupRelationPO;
import com.unipus.digitalbook.model.po.chapter.ChapterNodePO;
import com.unipus.digitalbook.model.po.chapter.ChapterPO;
import com.unipus.digitalbook.model.po.chapter.ChapterTemplatePO;
import com.unipus.digitalbook.model.po.chapter.ChapterVersionPO;
import com.unipus.digitalbook.publisher.standalone.ChapterEventPublisher;
import com.unipus.digitalbook.service.*;
import jakarta.annotation.Nullable;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.MessageFormat;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * 章节业务实现类
 */
@Service
@Slf4j
public class ChapterServiceImpl implements ChapterService {
    @Resource
    private ChapterPOMapper chapterPOMapper;
    @Resource
    private ChapterVersionPOMapper chapterVersionPOMapper;
    @Resource
    private UserService userService;
    @Resource
    private BookOperationLogService operationLogService;
    @Resource
    private BookPermissionService bookPermissionService;
    @Resource
    private ChapterEventPublisher chapterEventPublisher;
    @Resource
    private QuestionService questionService;
    @Resource
    private ChapterQuestionGroupRelationPOMapper chapterQuestionGroupRelationPOMapper;

    @Resource
    private BookVersionChapterVersionRelationPOMapper bookVersionChapterVersionRelationPOMapper;
    @Resource
    private PaperVersionService paperVersionService;
    @Resource
    private PaperReferenceService paperReferenceService;
    @Resource
    private ChapterTemplatePOMapper chapterTemplatePOMapper;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private LoadingCache<Long, Map<String, ChapterNode>> chapterNodeCache;
    @Resource
    private COSService cosService;
    @Override
    public ChapterList getChaptersByBookId(String bookId) {
        if (StringUtils.isBlank(bookId)) {
            return new ChapterList();
        }
        List<ChapterPO> chapterPOS = chapterPOMapper.selectByBookId(bookId);
        if (CollectionUtils.isEmpty(chapterPOS)) {
            return new ChapterList();
        }
        List<Chapter> chapters = chapterPOS.stream().map(ChapterPO::toEntity).sorted(Comparator.comparing(Chapter::getChapterNumber)).toList();
        return new ChapterList(chapters);
    }

    @Override
    public List<String> getChapterIdsByBookId(String bookId) {
        if (StringUtils.isBlank(bookId)) {
            return Collections.emptyList();
        }
        return chapterPOMapper.selectByBookId(bookId).stream().map(ChapterPO::getId).toList();
    }

    /**
     * 添加章节版本
     * @param chapterVersion 章节内容
     * @return 返回新添加的章节版本的CODE
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String addChapterVersion(ChapterVersion chapterVersion) {
        Long createBy = chapterVersion.getCreateBy();
        String chapterId = chapterVersion.getChapterId();
        log.debug("开始添加章节版本，chapterId={},createBy={}", chapterId, createBy);
        ChapterPO chapterPO = chapterPOMapper.selectByPrimaryKey(chapterVersion.getChapterId());
        if (chapterPO == null) {
            throw new IllegalArgumentException("章节不存在");
        }
        String bookId = chapterPO.getBookId();
        ChapterVersionPO po = new ChapterVersionPO();
        po.fromEntity(chapterVersion);
        po.setName(chapterPO.getName());
        // 上传章节内容到COS
        addChapterContentUploadUrl(bookId, po);
        int row = chapterVersionPOMapper.insertSelective(po);
        addChapterQuestions(chapterVersion.getQuestionList(), po.getId(), po.getCreateBy());
        addChapterPapers(bookId, chapterId, po.getId(), po.getCreateBy(), chapterVersion.getPaperReferenceList());
        if (row>0) {
            // 记录操作日志
            operationLogService.log(bookId, createBy, MessageFormat.format("编写“{0}”的内容", chapterPO.getName()), BookOperationEnum.SAVE.getCode());
            // 发布章节新增版本消息
            chapterEventPublisher.chapterEventPublisher(bookId, chapterId, EventTypeEnum.ADD_VERSION, createBy);
            return po.getVersionNumber();
        } else {
            log.error("添加章节版本失败，chapterId={},createBy={}", chapterId, createBy);
            throw new ChapterCreationException();
        }
    }

    private void addChapterQuestions(List<BigQuestionGroup> questionList, Long versionChapterId, Long currentUserId) {
        List<Long> newGroupIds = questionService.batchSaveBigQuestions(questionList);
        if (newGroupIds.isEmpty()) {
            return;
        }
        // 保存章节版本的ID和题的关系
        List<ChapterQuestionGroupRelationPO> relationPOList = newGroupIds.stream().map(
                groupId -> new ChapterQuestionGroupRelationPO(groupId, versionChapterId, currentUserId))
                .toList();
        chapterQuestionGroupRelationPOMapper.batchInsert(relationPOList);
    }

    /**
     * 添加章节中的试卷引用
     * 添加新的引用，删除无效的引用，更新已有的引用
     * @param bookId 教材ID
     * @param chapterId 章节ID
     * @param versionChapterId 章节版本ID
     * @param currentUserId 当前用户ID
     * @param paperReferenceList 试卷引用列表
     */
    private void addChapterPapers(String bookId, String chapterId, Long versionChapterId, Long currentUserId,
                                  List<PaperReference> paperReferenceList){
        if(CollectionUtils.isEmpty(paperReferenceList)){
            paperReferenceService.removePaperReferenceInChapter(bookId, chapterId, currentUserId);
            log.debug("章节无试卷引用，删除无效的引用");
            return;
        }
        paperReferenceList.forEach(paperReference -> {
            paperReference.setBookId(bookId);
            paperReference.setChapterId(chapterId);
        });

        // 保存章节中试卷的引用(编辑态引用关系)
        paperReferenceService.savePaperReference(paperReferenceList, currentUserId);

        // 生成章节中试卷的引用版本(引用关系版本化)
        paperReferenceService.savePaperReferenceWithChapterVersion(chapterId, versionChapterId, currentUserId);
    }

    /**
     * 上传章节内容到COS
     *
     * @param bookId           教材ID
     * @param chapterVersionPO 章节版本PO
     */
    public void addChapterContentUploadUrl(String bookId, ChapterVersionPO chapterVersionPO) {
        if (chapterVersionPO == null) {
            return;
        }
        if (StringUtils.isBlank(chapterVersionPO.getContent())
                && StringUtils.isBlank(chapterVersionPO.getStudentContent())) {
            return;
        }
        try (ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor()) {
            List<CompletableFuture<Void>> futures = new ArrayList<>();

            String content = chapterVersionPO.getContent();
            if (StringUtils.isNotBlank(content) && JSON.isValid(content)) {
                futures.add(CompletableFuture.runAsync(() -> {
                    String fileName = String.format("%s/%s/%s/content.json", bookId, chapterVersionPO.getChapterId(), chapterVersionPO.getVersionNumber());
                    String contentUrl = cosService.getPrivateUploadContentUrl(fileName, content);
                    chapterVersionPO.setContent(contentUrl);
                    log.info("上传章节内容到COS,bookId={},chapterId={},versionNumber={},url={}", bookId, chapterVersionPO.getChapterId(), chapterVersionPO.getVersionNumber(), chapterVersionPO.getContent());
                }, executor));
            }

            String studentContent = chapterVersionPO.getStudentContent();
            if (StringUtils.isNotBlank(studentContent) && JSON.isValid(studentContent)) {
                futures.add(CompletableFuture.runAsync(() -> {
                    String fileName = String.format("%s/%s/%s/studentContent.json", bookId, chapterVersionPO.getChapterId(), chapterVersionPO.getVersionNumber());
                    String studentContentUrl = cosService.getPrivateUploadContentUrl(fileName, studentContent);
                    chapterVersionPO.setStudentContent(studentContentUrl);
                    log.info("上传章节学生内容到COS,bookId={},chapterId={},versionNumber={},url={}", bookId, chapterVersionPO.getChapterId(), chapterVersionPO.getVersionNumber(), chapterVersionPO.getStudentContent());
                }, executor));
            }
            // 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        }
    }

    /**
     * 为章节内容设置预签名URL
     *
     * @param chapterVersionPOList 章节版本PO
     */
    public void getChapterContentPresignedUrl(List<ChapterVersionPO> chapterVersionPOList) {
        chapterVersionPOList.forEach(this::getChapterContentPresignedUrl);
    }


    /**
     * 为章节内容设置预签名URL
     *
     * @param chapterVersionPO 章节版本PO
     */
    public void getChapterContentPresignedUrl(ChapterVersionPO chapterVersionPO) {
        if (chapterVersionPO == null) {
            return;
        }
        if (StringUtils.isBlank(chapterVersionPO.getContent())
                && StringUtils.isBlank(chapterVersionPO.getStudentContent())) {
            return;
        }
        // 使用虚拟线程
        try (ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor()) {
            List<CompletableFuture<Void>> futures = new ArrayList<>();

            String content = chapterVersionPO.getContent();
            if (StringUtils.isNotBlank(content) && !JSON.isValid(content)) {
                futures.add(CompletableFuture.runAsync(() -> chapterVersionPO.setContent(
                        cosService.getCachePresignedUrlByUrl(content)), executor));
            }

            String studentContent = chapterVersionPO.getStudentContent();
            if (StringUtils.isNotBlank(studentContent) && !JSON.isValid(studentContent)) {
                futures.add(CompletableFuture.runAsync(() -> chapterVersionPO.setStudentContent(
                        cosService.getCachePresignedUrlByUrl(studentContent)), executor));
            }
            // 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        }
    }



    /**
     * 根据URL获取章节内容
     *
     * @param contentUrl 内容URL地址
     * @return 章节内容字符串，如果URL为空则返回null
     */
    public String getChapterContentByUrl(String contentUrl) {
        return cosService.getCacheDownloadContentByUrl(contentUrl,
                CacheConstant.REDIS_COS_CHAPTER_CONTENT_PREFIX,
                CacheConstant.REDIS_CHAPTER_CONTENT_TIMEOUT_SECONDS);
    }

    /**
     * 为所有章节内容设置上传URL
     */
    @Override
    public void batchAddChapterContentUploadUrl(String chapterId) {
        List<ChapterPO> chapterPOList = new ArrayList<>();
        if (StringUtils.isBlank(chapterId)) {
            chapterPOList = chapterPOMapper.selectAll();
        } else {
            ChapterPO chapterPO = chapterPOMapper.selectByPrimaryKey(chapterId);
            if (chapterPO != null) {
                chapterPOList.add(chapterPO);
            }
        }
        for (ChapterPO chapterPO : chapterPOList) {
            String bookId = chapterPO.getBookId();
            List<ChapterVersionPO> chapterVersionPOS = chapterVersionPOMapper.selectChapterContentByChapterId(chapterPO.getId());
            for (ChapterVersionPO chapterVersionPO : chapterVersionPOS) {
                if ((StringUtils.isNotBlank(chapterVersionPO.getContent()) && JSON.isValid(chapterVersionPO.getContent()))
                        || (StringUtils.isNotBlank(chapterVersionPO.getStudentContent()) && JSON.isValid(chapterVersionPO.getStudentContent()))) {
                    addChapterContentUploadUrl(bookId, chapterVersionPO);
                    chapterVersionPOMapper.updateChapterContentById(chapterVersionPO);
                }
            }
        }
    }

    /**
     * 根据章节ID获取最新版本的章节内容
     *
     * @param chapterId 章节ID
     * @return 最新版本的ChapterWithContentDTO对象
     */
    @Override
    public Chapter getLatestVersionByChapterId(String chapterId) {
        // 检查章节ID是否为空，如果为空则抛出异常
        if (chapterId == null) {
            throw new IllegalArgumentException("参数不能为空");
        }

        // 通过章节ID查询章节信息
        ChapterPO chapterPO = chapterPOMapper.selectByPrimaryKey(chapterId);

        // 如果查询结果为空，说明该章节不存在，抛出异常
        if (chapterPO == null) {
            throw new IllegalArgumentException("章节不存在");
        }

        // 检查章节是否有效，如果无效（已删除），则抛出异常
        if (Boolean.FALSE.equals(chapterPO.getEnable())) {
            throw new IllegalArgumentException("章节已删除");
        }

        // 创建一个新的Chapter对象
        Chapter chapter = new Chapter();

        // 将ChapterPO中的信息填充到Chapter对象中
        chapterPO.populateEntity(chapter);

        // 查询该章节的最新版本信息
        ChapterVersionPO chapterVersionPO = chapterVersionPOMapper.selectLatestVersionByChapterId(chapterId);

        // 如果存在最新版本，则将其转换为ChapterVersion对象，并设置到Chapter对象中
        if (chapterVersionPO != null) {
            // 为章节内容设置预签名URL
            getChapterContentPresignedUrl(chapterVersionPO);
            ChapterVersion chapterVersion = chapterVersionPO.toEntity();
            chapter.setChapterVersion(chapterVersion);
        }

        // 返回包含最新版本信息的Chapter对象
        return chapter;
    }

    /**
     * 编辑章节信息
     *
     * @param chapter 章节
     * @param userId 用户ID
     * @return 操作是否成功
     */
    @Override
    public Boolean editChapterInfo(Chapter chapter, Long userId) {
        ChapterPO chapterPO = chapterPOMapper.selectByPrimaryKey(chapter.getId());
        if (chapterPO == null){
            throw new IllegalArgumentException("章节不存在");
        }
        if (Boolean.FALSE.equals(chapterPO.getEnable())){
            throw new IllegalArgumentException("章节已删除");
        }
        String bookId = chapterPO.getBookId();
        ChapterPO po=new ChapterPO();
        po.setUpdateBy(userId);
        po.fromEntity(chapter);
        int rows = chapterPOMapper.updateByPrimaryKeySelective(po);
        if (rows>0) {
            if (chapter.getName() != null && !chapter.getName().equals(chapterPO.getName())) {
                // 记录操作日志
                operationLogService.log(bookId, userId, MessageFormat.format("将章节名称从“{0}”变更为“{1}”", chapterPO.getName(), chapter.getName()), BookOperationEnum.UPDATE.getCode());
            }
            // 发布章节编辑信息
            chapterEventPublisher.chapterEventPublisher(bookId, chapter.getId(), EventTypeEnum.EDIT, userId);
        }
        return true;
    }

    /**
     * 删除章节
     *
     * @param chapterId 章节ID
     * @param userId 用户ID
     */
    @Override
    public Boolean deleteChapter(String chapterId, Long userId) {
        ChapterPO chapterPO = chapterPOMapper.selectByPrimaryKey(chapterId);
        if (chapterPO == null){
            throw new IllegalArgumentException("章节不存在");
        }
        if (Boolean.FALSE.equals(chapterPO.getEnable())){
            throw new IllegalArgumentException("章节已删除");
        }
        chapterPO.setUpdateBy(userId);
        chapterPO.setEnable(false);
        int count = chapterPOMapper.updateByPrimaryKeySelective(chapterPO);
        if(count==0) {
            throw new IllegalArgumentException("删除章节失败");
        }
        String bookId = chapterPO.getBookId();
        // 删除章节引用的试卷(删除编辑态引用)
        paperReferenceService.removePaperReferenceInChapter(bookId, chapterId, userId);
        // 删除章节的协作者
        BookCollaboratorParam bookCollaboratorParam = new BookCollaboratorParam();
        bookCollaboratorParam.setBookId(chapterPO.getBookId());
        bookCollaboratorParam.setChapterId(chapterId);
        // 设置为空，删除该所有协作者
        bookCollaboratorParam.setUserIds(new ArrayList<>());
        bookPermissionService.updateBookCollaborator(bookCollaboratorParam, userId);
        // 记录操作日志
        operationLogService.log(bookId, userId, MessageFormat.format("删除“{0}”", chapterPO.getName()), BookOperationEnum.DELETE.getCode());
        // 发布章节删除信息
        chapterEventPublisher.chapterEventPublisher(bookId, chapterId, EventTypeEnum.DELETE, userId);
        return true;
    }

    @Override
    public Boolean existChapter(Collection<String> chapterIds) {
        return chapterPOMapper.existsChapter(chapterIds);
    }

    @Override
    public List<Chapter> getLatestChapterByBookId(String bookId) {
        if (StringUtils.isBlank(bookId)) {
            return Collections.emptyList();
        }
        List<ChapterPO> chapterPOS = chapterPOMapper.selectByBookId(bookId);
        if (chapterPOS.isEmpty()) {
            return Collections.emptyList();
        }
        List<String> chapterIds = chapterPOS.stream().map(ChapterPO::getId).toList();
        List<ChapterVersionPO> chapterVersionPOS = chapterVersionPOMapper.selectLatestVersionByChapterIds(chapterIds);
        // 为章节内容设置预签名URL
        getChapterContentPresignedUrl(chapterVersionPOS);
        Map<String, ChapterVersionPO> chapterVersionPOMap = chapterVersionPOS.stream().collect(Collectors.toMap(ChapterVersionPO::getChapterId, chapterVersionPO -> chapterVersionPO));
        return chapterPOS.stream().parallel().map(chapterPO ->
                        chapterPO.toEntity(chapterVersionPOMap.get(chapterPO.getId())).generateVersionHashCode())
                .sorted(Comparator.comparing(Chapter::getChapterNumber)).toList();
    }

    /**
     * 根据章节ID获取章节信息
     *
     * @param chapterId 章节ID
     * @return 章节对象，如果章节不存在或无效则返回null
     */
    @Override
    public Chapter getChapterById(String chapterId) {
        // 根据章节ID查询数据库中的章节PO对象
        ChapterPO chapterPO = chapterPOMapper.selectByPrimaryKey(chapterId);
        if (chapterPO != null) {
            // 检查章节是否被删除 如果章节被删除，返回null
            if (Boolean.FALSE.equals(chapterPO.getEnable())) {
                return null;
            }
            // 将章节PO对象转换为Chapter实体对象并返回
            return chapterPO.toEntity();
        }
        // 如果数据库中查不到章节，返回null
        return null;
    }

    /**
     * 根据章节ID和章节版本ID,获取章节信息
     *
     * @param versionId 版本ID
     * @return 章节对象，如果章节不存在或无效则返回null
     */
    @Override
    public Chapter getChapterWithVersionByVersionId(Long versionId) {
        // 根据章节ID查询数据库中的章节PO对象
        ChapterVersionPO chapterVersionPO = chapterVersionPOMapper.selectByPrimaryKey(versionId);
        if (chapterVersionPO == null) {
            return null;
        }
        String chapterId = chapterVersionPO.getChapterId();
        ChapterPO chapterPO = chapterPOMapper.selectByPrimaryKey(chapterId);
        if (chapterPO != null) {
            // 检查章节是否被删除 如果章节被删除，返回null
            if (Boolean.FALSE.equals(chapterPO.getEnable())) {
                return null;
            }
            // 将章节PO对象转换为Chapter实体对象并返回
            Chapter chapter = chapterPO.toEntity();
            // 为章节内容设置预签名URL
            getChapterContentPresignedUrl(chapterVersionPO);
            chapter.setChapterVersion(chapterVersionPO.toEntity());
            return chapter;
        }
        return null;
    }

    /**
     * 根据章节ID和章节版本号,获取章节信息
     *
     * @param chapterId     章节id
     * @param versionNumber 版本号
     * @return 章节对象，如果章节不存在或无效则返回null
     */
    @Override
    public Chapter getChapterWithVersionByIdAndVersionNumber(String chapterId, String versionNumber) {
        // 根据章节ID查询数据库中的章节PO对象
        ChapterPO chapterPO = chapterPOMapper.selectByPrimaryKey(chapterId);
        if (chapterPO != null) {
            // 检查章节是否被删除 如果章节被删除，返回null
            if (Boolean.FALSE.equals(chapterPO.getEnable())) {
                return null;
            }
            // 将章节PO对象转换为Chapter实体对象并返回
            Chapter chapter= chapterPO.toEntity();
            ChapterVersionPO chapterVersionPO = chapterVersionPOMapper.selectByChapterIdAndVersionNumber(chapterId, versionNumber);
            if (chapterVersionPO != null) {
                // 为章节内容设置预签名URL
                getChapterContentPresignedUrl(chapterVersionPO);
                chapter.setChapterVersion(chapterVersionPO.toEntity());
            }
            return chapter;
        }
        return null;
    }

    @Override
    public List<Long> getQuestionIds(String chapterId, String versionNumber) {
        if (StringUtils.isBlank(chapterId)) {
            return Collections.emptyList();
        }
        Long versionChapterId = chapterVersionPOMapper.selectIdByChapterIdAndVersionNumber(chapterId, versionNumber);
        if (versionChapterId == null) {
            return Collections.emptyList();
        }
        return chapterQuestionGroupRelationPOMapper.selectQuestionIdsByVersionChapterId(versionChapterId);
    }

    @Override
    public Long getQuestionId(String chapterId, String versionNumber, String questionId) {
        Long versionChapterId = chapterVersionPOMapper.selectIdByChapterIdAndVersionNumber(chapterId, versionNumber);
        return chapterQuestionGroupRelationPOMapper.selectQuestionIdByVersionChapterIdAndBizGroupId(versionChapterId, questionId);
    }

    /**
     * 根据教材版本ID,获取章节列表
     *
     * @param bookVersionId 教材版本ID
     * @return 章节列表
     */
    @Override
    public List<Chapter> getChapterListByBookVersionId(Long bookVersionId) {
        List<BookVersionChapterVersionRelationPO>  bookVersionChapterVersionRelationPOList = bookVersionChapterVersionRelationPOMapper.selectByBookVersionId(bookVersionId);
        if (!CollectionUtils.isEmpty(bookVersionChapterVersionRelationPOList)) {
            List<Chapter> chapters = buildChapterList(bookVersionId, bookVersionChapterVersionRelationPOList);
            log.debug("根据教材版本ID:{}获取章节列表成功,章节列表:{}", bookVersionId, chapters);
            return chapters;
        }
        return Collections.emptyList();
    }


    @Override
    public List<ChapterVersion> getLatestResourceByBookId(String bookId) {
        List<Long> latestChapterVersionIds = findLatestChapterVersionIds(bookId);
        if (CollectionUtils.isEmpty(latestChapterVersionIds)) {
            return Collections.emptyList();
        }
        List<ChapterVersionPO> resourceVersions = chapterVersionPOMapper.selectResourceByIds(latestChapterVersionIds);
        return toChapterVersionEntities(resourceVersions);
    }

    @Override
    public List<ChapterVersion> getLatestCatalogByBookId(String bookId) {
        List<Long> latestChapterVersionIds = findLatestChapterVersionIds(bookId);
        if (CollectionUtils.isEmpty(latestChapterVersionIds)) {
            return Collections.emptyList();
        }
        List<ChapterVersionPO> catalogVersions = chapterVersionPOMapper.selectCatalogByIds(latestChapterVersionIds);
        return toChapterVersionEntities(catalogVersions);
    }

    /**
     * 查找指定图书的所有章节最新版本ID列表
     */
    private List<Long> findLatestChapterVersionIds(String bookId) {
        if (StringUtils.isBlank(bookId)) {
            return Collections.emptyList();
        }
        List<String> chapterIds = chapterPOMapper.selectIdsByBookId(bookId);
        if (CollectionUtils.isEmpty(chapterIds)) {
            return Collections.emptyList();
        }
        return chapterVersionPOMapper.selectLatestVersionIdByChapterIds(chapterIds);
    }

    /**
     * 将PO列表转成实体列表
     */
    private List<ChapterVersion> toChapterVersionEntities(List<ChapterVersionPO> chapterVersionPOS) {
        if (CollectionUtils.isEmpty(chapterVersionPOS)) {
            return Collections.emptyList();
        }
        // 为章节内容设置预签名URL
        getChapterContentPresignedUrl(chapterVersionPOS);
        return chapterVersionPOS.parallelStream()
                .map(ChapterVersionPO::toEntity)
                .toList();
    }

    private List<Chapter> buildChapterList(Long bookVersionId, List<BookVersionChapterVersionRelationPO> bookVersionChapterVersionRelationPOList) {
        //获取所有的章节版本数据（章节内容）
        Map<Long, BookVersionChapterVersionRelationPO> chapterVersionRelationMap = bookVersionChapterVersionRelationPOList.stream()
                .collect(Collectors.toMap(BookVersionChapterVersionRelationPO::getChapterVersionId, o -> o));
        List<Long> chapterVersionIds = bookVersionChapterVersionRelationPOList.stream().map(BookVersionChapterVersionRelationPO::getChapterVersionId).toList();
        List<ChapterVersionPO> chapterVersionPOS = chapterVersionPOMapper.selectVersionListByIdList(chapterVersionIds);
        if (CollectionUtils.isEmpty(chapterVersionPOS)){
            return new ArrayList<>();
        }
        // todo 待优化并发
        Map<Long, List<BigQuestionGroup>> chapterVersionQuestionMap = chapterVersionIds.stream()
                .collect(Collectors.toMap(
                        chapterVersionId -> chapterVersionId,
                        chapterVersionId -> {
                            List<Long> questionIds = chapterQuestionGroupRelationPOMapper.selectQuestionIdsByVersionChapterId(chapterVersionId);
                            return questionService.batchGetBigQuestions(questionIds);
                        }
                ));
        // 处理教材下的卷子版本数据
        Map<Long, List<Paper>> paperChapterVersionMapping = paperVersionService.getPaperChapterVersionMapping(chapterVersionIds, null, bookVersionId);
        List<Chapter> chapters = new ArrayList<>();
        chapterVersionPOS.forEach(chapterVersionPO -> {
            Chapter chapter = new Chapter();
            chapter.setId(chapterVersionPO.getChapterId());
            // 获取版本
            BookVersionChapterVersionRelationPO relation = chapterVersionRelationMap.get(chapterVersionPO.getId());
            chapter.setName(relation.getChapterName());
            chapter.setChapterNumber(relation.getChapterNumber());
            // 为章节内容设置预签名URL
            getChapterContentPresignedUrl(chapterVersionPO);
            ChapterVersion entity = chapterVersionPO.toEntity();
            entity.setName(relation.getChapterName());
            entity.setQuestionList(chapterVersionQuestionMap.get(chapterVersionPO.getId()));
            entity.setPaperList(paperChapterVersionMapping.get(chapterVersionPO.getId()));
            chapter.setChapterVersion(entity);
            chapters.add(chapter);
        });
        return chapters.stream().sorted(Comparator.comparing(Chapter::getChapterNumber)).toList();
    }


    /**
     * 根据章节ID获取章节所有的版本
     *
     * @param chapterId 章节ID
     * @return 章节版本列表
     */
    @Override
    public List<ChapterVersion> getVersionListByChapterId(String chapterId) {
        ChapterPO chapterPO = chapterPOMapper.selectByPrimaryKey(chapterId);
        if (chapterPO == null) {
            throw new IllegalArgumentException("章节不存在");
        }
        if (Boolean.FALSE.equals(chapterPO.getEnable())) {
            throw new IllegalArgumentException("章节已删除");
        }
        List<ChapterVersionPO> chapterVersionPOS = chapterVersionPOMapper.selectByChapterId(chapterId);
        if (CollectionUtils.isEmpty(chapterVersionPOS)) {
            return new ArrayList<>();
        }
        // 为章节内容设置预签名URL
        getChapterContentPresignedUrl(chapterVersionPOS);
        List<ChapterVersion> chapterVersions = chapterVersionPOS.stream().map(ChapterVersionPO::toEntity).toList();
        List<Long> creatorIdList = chapterVersions.stream().map(ChapterVersion::getCreateBy).toList();
        Map<Long, UserInfo> userMap = userService.getUserMap(creatorIdList);
        chapterVersions.forEach(chapterVersion -> {
            UserInfo userInfo = userMap.get(chapterVersion.getCreateBy());
            if (userInfo != null) {
                chapterVersion.setCreatorName(userInfo.getName());
            }
        });
        return chapterVersions;
    }

    /**
     * 根据章节版本ID,获取章节版本信息
     *
     * @param chapterVersionId 根据章节版本ID
     * @return 章节版本信息
     */
    @Override
    public ChapterVersion getVersionByChapterVersionId(Long chapterVersionId) {
        ChapterVersionPO chapterVersionPO = chapterVersionPOMapper.selectByPrimaryKey(chapterVersionId);
        if (chapterVersionPO == null) {
            return null;
        }
        // 为章节内容设置预签名URL
        getChapterContentPresignedUrl(chapterVersionPO);
        return chapterVersionPO.toEntity();
    }

    /**
     * 根据章节ID获取章节版本总数量
     *
     * @param chapterId 章节ID
     * @return 章节版本总数量
     */
    @Override
    public Integer getVersionCountByChapterId(String chapterId) {
        return chapterVersionPOMapper.countByChapterId(chapterId);
    }

    /**
     * 根据章节ID分页获取章节版本列表
     *
     * @param chapterId  章节ID
     * @param pageParams 分页参数
     * @return 章节版本列表
     */
    @Override
    public List<ChapterVersion> getVersionPageListByChapterId(String chapterId, PageParams pageParams) {
        // 分页查询章节版本列表
        List<ChapterVersionPO> chapterVersionPOList = chapterVersionPOMapper.selectPageByChapterId(
                chapterId, pageParams);
        // 为章节内容设置预签名URL
        getChapterContentPresignedUrl(chapterVersionPOList);
        return chapterVersionPOList.stream().map(ChapterVersionPO::toEntity).toList();
    }

    /**
     * 根据章节ID获取最新的章章节版本
     *
     * @param chapterId 章节ID
     * @return 新的章章节版本对象
     */
    @Override
    public ChapterVersion getLatestChapterVersionByChapterId(String chapterId) {
        ChapterVersionPO chapterVersionPO = chapterVersionPOMapper.selectLatestVersionByChapterId(chapterId);
        if (chapterVersionPO == null) {
            return null;
        }
        // 为章节内容设置预签名URL
        getChapterContentPresignedUrl(chapterVersionPO);
        return chapterVersionPO.toEntity();
    }

    /**
     * 根据章节版本id列表,获取章节版本列表
     *
     * @param chapterVersionIdList 章节版本id列表
     * @return 章节版本列表
     */
    @Override
    public List<ChapterVersion> getChapterVersionByVersionIdList(List<Long> chapterVersionIdList) {
        List<ChapterVersionPO> chapterVersionPOS = chapterVersionPOMapper.selectVersionListByIdList(chapterVersionIdList);
        if (CollectionUtils.isEmpty(chapterVersionPOS)) {
            return new ArrayList<>();
        }
        // 为章节内容设置预签名URL
        getChapterContentPresignedUrl(chapterVersionPOS);
        return chapterVersionPOS.stream().map(ChapterVersionPO::toEntity).toList();
    }

    /**
     * 根据章节版本id获取章节信息
     *
     * @param bookVersionId  教材版本id
     * @param chapterId 章节id
     * @return 章节信息
     */
    @Override
    public Chapter getChapterByBookVersionIdAndChapterId(Long bookVersionId, String chapterId) {
        Chapter chapter = getChapterById(chapterId);
        if(chapter == null) {
            return null;
        }
        ChapterVersionPO chapterVersionPO = chapterVersionPOMapper.selectByBookVersionIdAndChapterId(bookVersionId, chapterId);
        if (chapterVersionPO == null) {
            return null;
        }
        // 为章节内容设置预签名URL
        getChapterContentPresignedUrl(chapterVersionPO);
        chapter.setChapterVersion(chapterVersionPO.toEntity());
        return chapter;
    }

    /**
     * 根据教材版本id获取章节名称列表
     *
     * @param bookVersionId 教材版本id
     * @return 章节名称列表
     */
    @Override
    public List<Chapter> getChapterNamesByBookVersionId(Long bookVersionId) {
        List<BookVersionChapterVersionRelationPO>  chapters = bookVersionChapterVersionRelationPOMapper.selectByBookVersionId(bookVersionId);
        List<Long> chapterVersionIds = chapters.stream().map(BookVersionChapterVersionRelationPO::getChapterVersionId).toList();
        Map<Long, String> chapterVersionIdMap = chapterVersionPOMapper.selectChapterIdsByIds(chapterVersionIds).stream().collect(Collectors.toMap(ChapterVersionPO::getId, ChapterVersionPO::getChapterId));
        return chapters.stream()
                .map(c -> c.toChapterEntity(chapterVersionIdMap.get(c.getChapterVersionId())))
                .sorted(Comparator.comparing(Chapter::getChapterNumber))
                .collect(Collectors.toList());
    }

    @Override
    public List<ChapterVersion> getCatalogByBookVersionId(Long bookVersionId) {
        List<BookVersionChapterVersionRelationPO>  chapters = bookVersionChapterVersionRelationPOMapper.selectByBookVersionId(bookVersionId);
        List<Long> chapterVersionIds = chapters.stream().map(BookVersionChapterVersionRelationPO::getChapterVersionId).toList();
        List<ChapterVersionPO> chapterVersionPOS = chapterVersionPOMapper.selectCatalogByIds(chapterVersionIds);
        return toChapterVersionEntities(chapterVersionPOS);
    }

    @Override
    public List<ChapterVersion> getResourceByBookVersionId(Long bookVersionId) {
        List<BookVersionChapterVersionRelationPO>  chapters = bookVersionChapterVersionRelationPOMapper.selectByBookVersionId(bookVersionId);
        List<Long> chapterVersionIds = chapters.stream().map(BookVersionChapterVersionRelationPO::getChapterVersionId).toList();
        List<ChapterVersionPO> resourceVersions = chapterVersionPOMapper.selectResourceByIds(chapterVersionIds);
        return toChapterVersionEntities(resourceVersions);
    }

    /**
     * 根据章节版本ID,获取章节版本首次上架时间
     *
     * @param chapterVersionId 章节版本ID
     * @return 章节版本首次上架时间
     */
    @Override
    public Date getFirstPublishedTimeByChapterVersionId(Long chapterVersionId) {
        BookVersionChapterVersionRelationPO relationPO = bookVersionChapterVersionRelationPOMapper.selectFirstByChapterVersionId(chapterVersionId);
        if (relationPO == null) {
            return null;
        }
        return relationPO.getCreateTime();
    }

    @Override
    public Map<String, ChapterNode> getNodeMapCache(Long chapterVersionId) {
        return chapterNodeCache.get(chapterVersionId);
    }

    @Override
    public ChapterNode getQuestionNodeByChapterVersionId(Long chapterVersionId, String questionId) {
        Map<String, ChapterNode> qiestionNodeIndexMap = new HashMap<>();
        return getNodeMapCache(chapterVersionId).values().parallelStream()
                .filter(c -> StringUtils.isNotBlank(c.getQuestionType()))
                .filter(c -> c.getText().equals(questionId)).findAny().orElse(null);
    }

    /**
     * 添加章节模版
     *
     * @param chapterTemplate 章节模版
     * @return 返回新添加的章节模版
     */
    @Nullable
    @Override
    public ChapterTemplate addChapterTemplate(ChapterTemplate chapterTemplate) {
        ChapterTemplatePO chapterTemplatePO = new ChapterTemplatePO();
        chapterTemplatePO.fromEntity(chapterTemplate);
        chapterTemplatePOMapper.insertSelective(chapterTemplatePO);
        if (chapterTemplatePO.getId() != null) {
            chapterTemplate.setId(chapterTemplatePO.getId());
            return chapterTemplate;
        }
        return null;
    }

    /**
     * 根据章节ID列表获取对应的模板列表
     *
     * @param chapterIdList 章节ID列表
     * @return List<ChapterTemplate> 章节模版列表
     */
    @Override
    public List<ChapterTemplate> getChapterTemplateListByChapterIdList(List<String> chapterIdList) {
        if (CollectionUtils.isEmpty(chapterIdList)) {
            return List.of();
        }
        List<ChapterTemplatePO> chapterTemplatePOS = chapterTemplatePOMapper.getChapterTemplateListByChapterIdList(chapterIdList);
        if (CollectionUtils.isEmpty(chapterTemplatePOS)) {
            return List.of();
        }
        return chapterTemplatePOS.stream().map(ChapterTemplatePO::toEntity).toList();
    }

    /**
     * 根据书籍ID获取对应的模板列表
     *
     * @param bookId 书籍ID
     * @return 模板列表
     */
    @Override
    public List<ChapterTemplate> getChapterTemplateListByBookId(String bookId) {
        List<Chapter> chapters = getLatestChapterByBookId(bookId);
        if (CollectionUtils.isEmpty(chapters)) {
            return List.of();
        }
        List<String> chapterIdList = chapters.stream().map(Chapter::getId).toList();
        List<ChapterTemplate> chapterTemplates = getChapterTemplateListByChapterIdList(chapterIdList);
        return chapterTemplates;
    }

    @Override
    public Long getChapterVersionIdByChapterIdAndVersionNumber(String chapterId, String versionNumber) {
        String key = String.format("chapterVersionId:%s:%s", chapterId, versionNumber);
        long cacheChapterVersionId = NumberUtils.toLong(stringRedisTemplate.opsForValue().get(key));
        if (cacheChapterVersionId > 0) {
            return cacheChapterVersionId;
        }
        Long chapterVersionId = chapterVersionPOMapper.selectVersionIdByChapterIdAndVersionNumber(chapterId, versionNumber);
        stringRedisTemplate.opsForValue().set(key, chapterVersionId.toString(), Duration.ofDays(1));
        return chapterVersionId;
    }
}
