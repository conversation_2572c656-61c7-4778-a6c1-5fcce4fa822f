package com.unipus.digitalbook.service.converter.tenant;

import com.alibaba.fastjson2.TypeReference;

public abstract class DefaultOne2TwoBodyConverter<ONE, TWO, THREE> extends ThreeGenericBodyConverter<ONE, TWO, THREE>{
    public DefaultOne2TwoBodyConverter(TypeReference<ONE> oneTypeReference, TypeReference<TWO> twoTypeReference, TypeReference<THREE> threeTypeReference) {
        super(oneTypeReference, twoTypeReference, threeTypeReference);
    }

    @Override
    public TWO oneToTwo(ONE one) {
        return null;
    }
}
