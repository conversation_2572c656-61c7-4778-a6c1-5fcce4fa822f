package com.unipus.digitalbook.service.converter.tenant;

import com.alibaba.fastjson2.TypeReference;

public abstract class DefaultTwo2ThreeBodyConverter<ONE, TWO, THREE> extends ThreeGenericBodyConverter<ONE, TWO, THREE>{
    public DefaultTwo2ThreeBodyConverter(TypeReference<ONE> oneTypeReference, TypeReference<TWO> twoTypeReference, TypeReference<THREE> threeTypeReference) {
        super(oneTypeReference, twoTypeReference, threeTypeReference);
    }

    @Override
    public THREE twoToThree(TWO two) {
        return null;
    }
}
