package com.unipus.digitalbook.service.converter.tenant;

import com.alibaba.fastjson2.TypeReference;
import lombok.Getter;

@Getter
public abstract class TwoGenericBodyConverter<ONE, TWO> extends BaseBodyConverter implements BodyConverter<ONE, TWO> {

    private final TypeReference<ONE> oneTypeReference;
    private final TypeReference<TWO> twoTypeReference;

    public TwoGenericBodyConverter(TypeReference<ONE> oneTypeReference, TypeReference<TWO> twoTypeReference) {
        this.oneTypeReference = oneTypeReference;
        this.twoTypeReference = twoTypeReference;
    }

    public TWO convert(ONE one) throws Exception {
        return convert(one, getTwo(), this::o2o);
    }

    public abstract TWO o2o(ONE one);

    public TypeReference<TWO> getTwo() {
        return twoTypeReference;
    }
}
