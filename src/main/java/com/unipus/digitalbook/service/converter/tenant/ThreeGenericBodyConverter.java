package com.unipus.digitalbook.service.converter.tenant;

import com.alibaba.fastjson2.TypeReference;
import lombok.Getter;

@Getter
public abstract class ThreeGenericBodyConverter<ONE, TWO, THREE> extends BaseBodyConverter implements BodyConverter<ONE, THREE> {

    private final TypeReference<ONE> oneTypeReference;
    private final TypeReference<TWO> twoTypeReference;
    private final TypeReference<THREE> threeTypeReference;

    public ThreeGenericBodyConverter(TypeReference<ONE> oneTypeReference,
                                     TypeReference<TWO> twoTypeReference,
                                     TypeReference<THREE> threeTypeReference) {
        this.oneTypeReference = oneTypeReference;
        this.twoTypeReference = twoTypeReference;
        this.threeTypeReference = threeTypeReference;
    }

    public THREE convert(ONE one) throws Exception {

        TWO two = convert(one, getTwo(), this::oneToTwo);

        return convert(two, getThree(), this::twoToThree);

    }

    public abstract TWO oneToTwo(ONE one);

    public abstract THREE twoToThree(TWO two);

    public TypeReference<TWO> getTwo() {
        return twoTypeReference;
    }

    public TypeReference<THREE> getThree() {
        return threeTypeReference;
    }
}
