package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.common.PageParams;
import com.unipus.digitalbook.model.entity.chapter.*;
import jakarta.annotation.Nullable;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 章节业务接口
 */
public interface ChapterService {

    /**
     * 根据教材ID获取章节列表
     *
     * @param bookId 教材ID
     * @return 章节列表
     */
    ChapterList getChaptersByBookId(String bookId);

    /**
     * 根据教材ID获取章节ID列表
     *
     * @param bookId 教材ID
     * @return 章节ID列表
     */
    List<String> getChapterIdsByBookId(String bookId);

    /**
     * 添加章节版本(保存章节内容)
     *
     * @param chapterVersion 章节内容
     * @return 返回新添加的章节版本的CODE
     */
    String addChapterVersion(ChapterVersion chapterVersion);

    /**
     * 为所有章节内容设置上传URL
     */
    void batchAddChapterContentUploadUrl(String chapterId);

    /**
     * 根据章节ID获取最新版本的章节内容
     *
     * @param chapterId 章节ID
     * @return 最新版本的ChapterWithContentDTO对象
     */
    Chapter getLatestVersionByChapterId(String chapterId);

    /**
     * 编辑章节信息
     *
     * @param chapter 章节信息
     * @param userId  操作人的用户ID
     * @return 操作是否成功
     */
    Boolean editChapterInfo(Chapter chapter, Long userId);

    /**
     * 根据章节ID获取章节所有的版本
     *
     * @param chapterId 章节ID
     * @return 章节所有的版本列表
     */
    List<ChapterVersion> getVersionListByChapterId(String chapterId);

    /**
     * 删除章节
     *
     * @param chapterId 章节ID
     * @param userId    用户ID
     * @return 是否删除成功
     */
    Boolean deleteChapter(String chapterId, Long userId);

    /**
     * 章节是否存在
     *
     * @param chapterIds 章节ID集合
     * @return 是否存在
     */
    Boolean existChapter(Collection<String> chapterIds);


    /**
     * 根据教材ID,获取最新版本的章节列表
     *
     * @param bookId 教材ID
     * @return 章节列表
     */
    List<Chapter> getLatestChapterByBookId(String bookId);

    /**
     * 根据章节ID,获取章节信息
     *
     * @param chapterId 章节ID
     * @return 章节信息
     */
    Chapter getChapterById(String chapterId);

    /**
     * 根据章节ID和章节版本ID,获取章节信息
     *
     * @param versionId 版本ID
     * @return 章节信息
     */
    Chapter getChapterWithVersionByVersionId(Long versionId);

    /**
     * 根据章节ID和章节版本号,获取章节信息
     *
     * @param chapterId     章节id
     * @param versionNumber 版本号
     * @return 章节信息
     */
    Chapter getChapterWithVersionByIdAndVersionNumber(String chapterId, String versionNumber);

    /**
     * 根据章节ID和章节版本号,获取章节题的列表
     *
     * @param chapterId     章节id
     * @param versionNumber 章节版本号
     * @return 题的id列表
     */
    List<Long> getQuestionIds(String chapterId, String versionNumber);

    /**
     * 根据章节ID和章节版本号以及题的业务id, 获取章节题的id
     *
     * @param chapterId     章节id
     * @param versionNumber 章节版本号
     * @param questionId    题的id
     * @return 章节信息
     */
    Long getQuestionId(String chapterId, String versionNumber, String questionId);

    /**
     * 根据教材版本ID,获取章节列表
     *
     * @param bookVersionId 教材版本ID
     * @return 章节列表
     */
    List<Chapter> getChapterListByBookVersionId(Long bookVersionId);

    /**
     * 根据教材ID,获取最新的章节目录
     *
     * @param bookId 教材id
     * @return 章节目录
     */
    List<ChapterVersion> getLatestCatalogByBookId(String bookId);

    /**
     * 根据教材ID,获取最新的章节资源
     *
     * @param bookId 教材id
     * @return 章节资源
     */
    List<ChapterVersion> getLatestResourceByBookId(String bookId);


    /**
     * 根据章节版本ID,获取章节版本信息
     *
     * @param chapterVersionId 根据章节版本ID
     * @return 章节版本信息
     */
    ChapterVersion getVersionByChapterVersionId(Long chapterVersionId);

    /**
     * 根据章节ID获取章节版本总数量
     *
     * @param chapterId 章节ID
     * @return 章节版本总数量
     */
    Integer getVersionCountByChapterId(String chapterId);

    /**
     * 根据章节ID分页获取章节版本列表
     *
     * @param chapterId  章节ID
     * @param pageParams 分页参数
     * @return 章节版本列表
     */
    List<ChapterVersion> getVersionPageListByChapterId(String chapterId, PageParams pageParams);

    /**
     * 根据章节ID获取最新的章章节版本
     *
     * @param chapterId 章节ID
     * @return 新的章章节版本对象
     */
    ChapterVersion getLatestChapterVersionByChapterId(String chapterId);

    /**
     * 根据教材ID和教材版本号,获取章节版本id列表
     *
     * @param chapterVersionIdList 章节版本id列表
     * @return 章节版本列表
     */
    List<ChapterVersion> getChapterVersionByVersionIdList(List<Long> chapterVersionIdList);

    /**
     * 根据教材版本号ID和章节id,获取章节信息
     *
     * @param bookVersionId 教材版本号
     * @param chapterId     章节id
     * @return 章节信息
     */
    Chapter getChapterByBookVersionIdAndChapterId(Long bookVersionId, String chapterId);

    /**
     * 根据教材版本号ID获取章节名字列表
     *
     * @param bookVersionId 教材版本号
     * @return 章节名字列表
     */
    List<Chapter> getChapterNamesByBookVersionId(Long bookVersionId);

    /**
     * 根据教材版本号ID获取教材的目录列表
     *
     * @param bookVersionId 教材版本号
     * @return 章节目录列表
     */
    List<ChapterVersion> getCatalogByBookVersionId(Long bookVersionId);

    /**
     * 根据教材版本号ID获取教材的资源列表
     *
     * @param bookVersionId 教材版本号
     * @return 章节资源列表
     */
    List<ChapterVersion> getResourceByBookVersionId(Long bookVersionId);

    /**
     * 根据章节版本ID,获取章节版本首次上架时间
     *
     * @param chapterVersionId 章节版本ID
     * @return 章节版本首次上架时间
     */
    Date getFirstPublishedTimeByChapterVersionId(Long chapterVersionId);

    /**
     * 根据章节版本ID,获取章节版本节点索引缓存
     * @param chapterVersionId 教材版本ID
     * @return 章节版本节点索引
     */
    Map<String, ChapterNode> getNodeMapCache(Long chapterVersionId);

    /**
     * 根据章节版本ID,获取章节版本题目节点索引缓存
     * @param chapterVersionId 教材版本ID
     * @return 节点索引缓存
     */
    ChapterNode getQuestionNodeByChapterVersionId(Long chapterVersionId, String questionId);

    //================章节模版==========================

    /**
     * 添加章节模版
     *
     * @param chapterTemplate 章节模版
     * @return 返回新添加的章节模版
     */
    @Nullable
    ChapterTemplate addChapterTemplate(ChapterTemplate chapterTemplate);


    /**
     * 根据章节ID列表获取对应的模板列表
     *
     * @param chapterIdList 章节ID列表
     * @return 模板列表
     */
    List<ChapterTemplate> getChapterTemplateListByChapterIdList(List<String> chapterIdList);

    /**
     * 根据书籍ID获取对应的模板列表
     *
     * @param bookId 书籍ID
     * @return 模板列表
     */
    List<ChapterTemplate> getChapterTemplateListByBookId(String bookId);

    /**
     * 根据章节ID和版本号,获取章节版本ID
     * @param chapterId 章节ID
     * @param versionNumber 版本号
     * @return 章节版本ID
     */
    Long getChapterVersionIdByChapterIdAndVersionNumber(String chapterId, String versionNumber);
}
