package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.entity.wordpractice.WordPractice;
import com.unipus.digitalbook.model.po.WordPracticePO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【word_practice(词汇学练)】的数据库操作Mapper
 * @createDate 2025-07-02 19:41:03
 * @Entity generator.domain.WordPracticePO
 */
public interface WordPracticePOMapper {

    List<WordPractice> selectList(String parentId, int offset, int limit);

    List<WordPracticePO> selectListByParentIds(List<String> parentIds);

    int selectCount(String parentId);

    int deleteByPrimaryKey(Long id);

    int insert(WordPracticePO wordPracticePO);

    int insertSelective(WordPracticePO wordPracticePO);

    WordPracticePO selectByPrimaryKey(Long id);

    WordPracticePO selectByBizId(String bizId);

    int updateByPrimaryKeySelective(WordPracticePO wordPracticePO);

    int updateByPrimaryKey(WordPracticePO wordPracticePO);

    Integer selectByName(String parentId, String name, Long id);

}
