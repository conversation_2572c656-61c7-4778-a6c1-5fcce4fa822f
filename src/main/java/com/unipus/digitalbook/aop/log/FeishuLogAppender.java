package com.unipus.digitalbook.aop.log;

import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.classic.spi.IThrowableProxy;
import ch.qos.logback.core.AppenderBase;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.Arrays;
import java.util.Map;

@Configuration
@Slf4j
public class FeishuLogAppender extends AppenderBase<ILoggingEvent> {

    private static final String webhookUrl = "https://open.feishu.cn/open-apis/bot/v2/hook/4a52098a-26e5-4f05-9ad3-331d75b1173c";
    private final WebClient webClient = WebClient.create();

    @Override
    protected void append(ILoggingEvent eventObject) {
        if (!eventObject.getLevel().isGreaterOrEqual(ch.qos.logback.classic.Level.ERROR)) {
            return;
        }
        Map<String, Object> body = Map.of(
                "msg_type", "text",
                "content", Map.of("text", String.format("【异常日志】%s\n%s", eventObject.getFormattedMessage(), getStackTrace(eventObject)))
        );

        webClient.post()
            .uri(webhookUrl)
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(body)
            .retrieve()
            .bodyToMono(String.class)
            .subscribe();
    }

    private String getStackTrace(ILoggingEvent event) {
        IThrowableProxy tp = event.getThrowableProxy();
        if ( tp==null) return "";

        StringBuilder sb = new StringBuilder();
        sb.append(tp.getClassName()).append(": ").append(tp.getMessage()).append("\n");

        Object[] array = Arrays.stream(tp.getStackTraceElementProxyArray()).toArray();
        Arrays.stream(array).forEach(p->sb.append("\t").append(p).append("\n"));

        return sb.toString();
    }
}