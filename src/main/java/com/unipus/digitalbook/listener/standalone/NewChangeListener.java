package com.unipus.digitalbook.listener.standalone;

import com.unipus.digitalbook.common.exception.business.BizException;
import com.unipus.digitalbook.listener.CustomRetryTemplate;
import com.unipus.digitalbook.model.events.TableChangeEvent;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;

import java.util.List;

/**
 * todo: 后续要删除
 * 测试变更事件监听器
 */
//@Component
@Slf4j
public class NewChangeListener{

    @Resource
    private CustomRetryTemplate customRetryTemplate;

    private static final List<String> TABLE_NAMES = List.of("organization");

    /**
     * 处理表变更事件
     * @param event 表变更事件
     */
    @EventListener
    public void handleTableChangeEvent(TableChangeEvent event) {
        if(!TABLE_NAMES.contains(event.getTableName())) {
            return;
        }

        customRetryTemplate.handle(event, (e)->{
            log.debug("测试事件监听, 触发事件: {}", event.getSource());
            throw new BizException("测试事件监听异常处理重试机制");
        }, getCallerInfo());
    }

    private String getCallerInfo(){
        return this.getClass().getSimpleName() + ".handleTableChangeEvent";
    }
}
