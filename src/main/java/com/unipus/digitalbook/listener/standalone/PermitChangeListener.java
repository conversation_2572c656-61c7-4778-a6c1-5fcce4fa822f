package com.unipus.digitalbook.listener.standalone;

import com.unipus.digitalbook.listener.CustomRetryTemplate;
import com.unipus.digitalbook.model.constants.CacheConstant;
import com.unipus.digitalbook.model.events.TableChangeEvent;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 组织变更事件监听器
 */
@Component
@Slf4j
public class PermitChangeListener{

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private CustomRetryTemplate customRetryTemplate;

    private static final List<String> TARGET_TABLE_LIST = List.of(
            "organization",
            "org_user_relation",
            "role",
            "role_permission_relation",
            "org_role_relation",
            "role_user_relation"
    );

    /**
     * 处理表变更事件
     * @param event 表变更事件
     */
    @EventListener
    public void handleTableChangeEvent(TableChangeEvent event) {
        if(!TARGET_TABLE_LIST.contains(event.getTableName())) {
            return;
        }
        customRetryTemplate.handle(event, (e)->{
            log.debug("正在删除接口权限缓存, 触发事件: {}", event.getSource());
            stringRedisTemplate.delete(CacheConstant.REDIS_API_PERMISSION_KEY);
            log.debug("接口权限缓存删除成功");
            return null;
        }, getCallerInfo());
    }

    private String getCallerInfo(){
        return this.getClass().getSimpleName() + ".handleTableChangeEvent";
    }
}
