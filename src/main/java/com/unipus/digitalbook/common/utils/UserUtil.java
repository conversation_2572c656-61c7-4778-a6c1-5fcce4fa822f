package com.unipus.digitalbook.common.utils;

import com.unipus.digitalbook.common.exception.user.UserAuthInfoException;
import com.unipus.digitalbook.model.constants.WebConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Optional;

@Component
@Slf4j
public class UserUtil {
    public static boolean isSuperAdmin(Long userId) {
        if (userId == null) {
            return false;
        }
        return 1 == userId;
    }

    public static Long getCurrentUser() {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes)RequestContextHolder.getRequestAttributes();
        if (requestAttributes == null) {
            throw new UserAuthInfoException("获取当前用户ID失败");
        }
        Object userId = requestAttributes.getRequest().getAttribute(WebConstant.JWT_USER_ID);
        if(!(userId instanceof Long)){
            throw new UserAuthInfoException("获取当前用户ID失败");
        }
        return (Long)userId;
    }

    /**
     * 获取当前操作者
     * - 优先获取JWT中的用户ID(系统用户ID)，如果不存在则获取JWT中的OpenId(平台用户ID)
     * @return 操作者ID
     */
    public static String getCurrentOperator() {
        String userId = getAttributeFromRequest(WebConstant.JWT_USER_ID);
        if (StringUtils.hasText(userId)) {
            return userId;
        }
        return getAttributeFromRequest(WebConstant.JWT_BACKEND_OID);
    }

    private static String getAttributeFromRequest(String attributeName) {
        return Optional.ofNullable(RequestContextHolder.getRequestAttributes())
                .map(attributes -> ((ServletRequestAttributes) attributes).getRequest())
                .map(request -> request.getAttribute(attributeName))
                .map(Object::toString)
                .orElse(null);
    }

    public static String desensitization(String mobile) {
        if (mobile == null || mobile.isEmpty()) {
            return null;
        }
        return mobile.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
    }
}
