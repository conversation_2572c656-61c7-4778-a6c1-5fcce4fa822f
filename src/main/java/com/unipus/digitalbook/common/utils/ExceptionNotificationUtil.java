package com.unipus.digitalbook.common.utils;

import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 异常通知工具类
 * 用于构建异常通知内容和提供异常解决建议
 */
@Slf4j
public class ExceptionNotificationUtil {

    /**
     * 构建异常通知内容（带请求信息）
     */
    public static String buildExceptionNotificationContent(Exception exception, String requestUrl, String requestMethod, String userAgent, String requestId) {
        return buildExceptionNotificationContent(exception, requestUrl, requestMethod, userAgent, requestId, null);
    }
    
    /**
     * 构建异常通知内容（带请求信息和环境信息）
     */
    public static String buildExceptionNotificationContent(Exception exception, String requestUrl, String requestMethod, String userAgent, String requestId, String environment) {
        StringBuilder content = new StringBuilder();
        
        // 基本信息
        content.append("**发生时间：** ").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");
        content.append("**👀 请求id：** ").append(requestId).append("\n");
        
        // 环境信息
        if (environment != null && !environment.trim().isEmpty()) {
            content.append("**🌍 运行环境：** ").append(getEnvironmentDisplayName(environment)).append("\n");
        }
        
        // 请求信息（使用传入的参数或尝试获取）
        if (requestUrl != null && requestMethod != null) {
            content.append("**请求URL：** ").append(requestMethod).append(" ").append(requestUrl).append("\n");
            if (userAgent != null) {
                content.append("**User-Agent：** ").append(userAgent).append("\n");
            }
        }
        // 上下文信息
        content.append("\n");
        
        // 异常根本原因分析
        content.append("**异常链分析：** \n");
        Throwable currentCause = exception;
        int causeLevel = 0;
        while (currentCause != null) {
            String prefix = "  ".repeat(causeLevel);
            if (causeLevel == 0) {
                content.append(prefix).append("🔥 当前异常：");
            } else if (causeLevel == 1) {
                content.append(prefix).append("🚨 直接原因：");
            } else {
                content.append(prefix).append("📍 深层原因：");
            }
            content.append(currentCause.getClass().getSimpleName())
                   .append(": ")
                   .append(currentCause.getMessage())
                   .append("\n");
            currentCause = currentCause.getCause();
            causeLevel++;
        }
        content.append("\n");
        
        // 根据异常类型提供解决建议
        String suggestion = getExceptionSuggestion(exception);
        if (suggestion != null) {
            content.append("**解决建议：** ").append(suggestion).append("\n\n");
        }
        
        // 堆栈信息（优化显示格式，突出关键信息）
        content.append("**详细堆栈：** \n");
        StackTraceElement[] stackTrace = exception.getStackTrace();
        
        // 先统计项目代码的数量
        int projectCodeCount = 0;
        for (StackTraceElement element : stackTrace) {
            if (element.getClassName().startsWith("com.unipus.digitalbook")) {
                projectCodeCount++;
            }
        }
        
        // 确保显示所有项目代码，同时限制总行数
        int maxLines = Math.min(10, stackTrace.length);
        int projectCodeShown = 0;
        
        for (int i = 0; i < maxLines; i++) {
            StackTraceElement element = stackTrace[i];
            String className = element.getClassName();
            String methodName = element.getMethodName();
            String fileName = element.getFileName();
            int lineNumber = element.getLineNumber();
            
            // 判断是否为项目相关代码
            boolean isProjectCode = className.startsWith("com.unipus.digitalbook");
            boolean isSpringFramework = className.startsWith("org.springframework");
            boolean isJavaCore = className.startsWith("java.") || className.startsWith("javax.");
            
            // 如果是项目代码，增加计数器
            if (isProjectCode) {
                projectCodeShown++;
            }
            
            content.append("  ");
            if (i == 0) {
                content.append("🔥 "); // 第一行用火焰图标突出
            } else if (isProjectCode) {
                // 检查是否是第一个项目代码位置
                boolean isFirstProjectCode = true;
                for (int j = 0; j < i; j++) {
                    if (stackTrace[j].getClassName().startsWith("com.unipus.digitalbook")) {
                        isFirstProjectCode = false;
                        break;
                    }
                }
                
                if (isFirstProjectCode) {
                    content.append("🚨 "); // 第一个项目代码位置用警报图标
                } else {
                    content.append("📍 "); // 其他项目代码用定位图标
                }
            } else if (isSpringFramework) {
                content.append("🌱 "); // Spring框架代码
            } else if (isJavaCore) {
                content.append("☕ "); // Java核心库
            } else {
                content.append("  "); // 其他代码用空格
            }
            
            // 简化类名显示
            String shortClassName = className;
            if (className.contains(".")) {
                shortClassName = className.substring(className.lastIndexOf(".") + 1);
            }
            
            content.append(shortClassName).append(".").append(methodName);
            
            if (fileName != null && lineNumber > 0) {
                content.append("(").append(fileName).append(":").append(lineNumber).append(")");
            }
            
            // 如果是项目代码，显示完整类名
            if (isProjectCode) {
                content.append("\n    └─ ").append(className);
            }
            
            content.append("\n");
        }
        
        // 如果前30行没有显示完所有项目代码，继续显示剩余的项目代码
        if (projectCodeShown < projectCodeCount && maxLines < stackTrace.length) {
            content.append("  📍 **继续显示剩余项目代码：** \n");
            
            for (int i = maxLines; i < stackTrace.length && projectCodeShown < projectCodeCount; i++) {
                StackTraceElement element = stackTrace[i];
                String className = element.getClassName();
                
                if (className.startsWith("com.unipus.digitalbook")) {
                    String methodName = element.getMethodName();
                    String fileName = element.getFileName();
                    int lineNumber = element.getLineNumber();
                    
                    content.append("  📍 ");
                    
                    // 简化类名显示
                    String shortClassName = className;
                    if (className.contains(".")) {
                        shortClassName = className.substring(className.lastIndexOf(".") + 1);
                    }
                    
                    content.append(shortClassName).append(".").append(methodName);
                    
                    if (fileName != null && lineNumber > 0) {
                        content.append("(").append(fileName).append(":").append(lineNumber).append(")");
                    }
                    
                    content.append("\n    └─ ").append(className).append("\n");
                    
                    projectCodeShown++;
                }
            }
        }

        return content.toString();
    }
    
    /**
     * 根据异常类型提供解决建议
     */
    public static String getExceptionSuggestion(Exception exception) {
        String exceptionName = exception.getClass().getSimpleName();
        String message = exception.getMessage();
        
        if (message == null) {
            message = "";
        }
        
        // 首先尝试基于堆栈信息的精确分析
        String stackBasedSuggestion = getStackBasedSuggestion(exception);
        if (stackBasedSuggestion != null) {
            return stackBasedSuggestion;
        }
        
        // 数据库相关异常
        if (isDatabaseException(exceptionName, message)) {
            return getDatabaseSuggestion(exceptionName, message);
        }
        
        // 网络连接相关异常
        if (isNetworkException(exceptionName, message)) {
            return getNetworkSuggestion(exceptionName, message);
        }
        
        // 空指针和数组相关异常
        if (isNullPointerOrArrayException(exceptionName, message)) {
            return getNullPointerOrArraySuggestion(exceptionName, message);
        }
        
        // 类型转换相关异常
        if (isTypeConversionException(exceptionName, message)) {
            return getTypeConversionSuggestion(exceptionName, message);
        }
        
        // 文件操作相关异常
        if (isFileOperationException(exceptionName, message)) {
            return getFileOperationSuggestion(exceptionName, message);
        }
        
        // 权限相关异常
        if (isPermissionException(exceptionName, message)) {
            return getPermissionSuggestion(exceptionName, message);
        }
        
        // 业务逻辑相关异常
        if (isBusinessException(exceptionName, message)) {
            return getBusinessSuggestion(exceptionName, message);
        }
        
        // Spring框架相关异常
        if (isSpringException(exceptionName, message)) {
            return getSpringSuggestion(exceptionName, message);
        }
        
        // 缓存相关异常
        if (isCacheException(exceptionName, message)) {
            return getCacheSuggestion(exceptionName, message);
        }
        
        // 消息队列相关异常
        if (isMessageQueueException(exceptionName, message)) {
            return getMessageQueueSuggestion(exceptionName, message);
        }
        
        // 默认建议
        return "检查代码逻辑、数据状态或系统配置";
    }
    
    /**
     * 基于堆栈信息的精确异常分析
     */
    private static String getStackBasedSuggestion(Exception exception) {
        StackTraceElement[] stackTrace = exception.getStackTrace();
        if (stackTrace == null || stackTrace.length == 0) {
            return null;
        }
        
        // 分析前几个堆栈元素
        for (int i = 0; i < Math.min(5, stackTrace.length); i++) {
            StackTraceElement element = stackTrace[i];
            String className = element.getClassName();
            String methodName = element.getMethodName();
            String fileName = element.getFileName();
            int lineNumber = element.getLineNumber();
            
            // 数据库操作相关
            if (isDatabaseOperation(className, methodName)) {
                return getDatabaseOperationSuggestion(className, methodName, fileName, lineNumber);
            }
            
            // HTTP请求相关
            if (isHttpOperation(className, methodName)) {
                return getHttpOperationSuggestion(className, methodName, fileName, lineNumber);
            }
            
            // 文件操作相关
            if (isFileOperation(className, methodName)) {
                return getFileOperationSuggestion(className, methodName, fileName, lineNumber);
            }
            
            // 缓存操作相关
            if (isCacheOperation(className, methodName)) {
                return getCacheOperationSuggestion(className, methodName, fileName, lineNumber);
            }
            
            // 消息队列操作相关
            if (isMessageQueueOperation(className, methodName)) {
                return getMessageQueueOperationSuggestion(className, methodName, fileName, lineNumber);
            }
            
            // 业务逻辑相关
            if (isBusinessOperation(className, methodName)) {
                return getBusinessOperationSuggestion(className, methodName, fileName, lineNumber);
            }
        }
        
        return null;
    }
    
    /**
     * 判断是否为数据库相关异常
     */
    private static boolean isDatabaseException(String exceptionName, String message) {
        return exceptionName.contains("SQL") || 
               exceptionName.contains("DataAccess") ||
               exceptionName.contains("Jdbc") ||
               exceptionName.contains("MyBatis") ||
               exceptionName.contains("Hibernate") ||
               message.toLowerCase().contains("sql") ||
               message.toLowerCase().contains("database") ||
               message.toLowerCase().contains("table") ||
               message.toLowerCase().contains("column") ||
               message.toLowerCase().contains("constraint") ||
               message.toLowerCase().contains("foreign key") ||
               message.toLowerCase().contains("primary key") ||
               message.toLowerCase().contains("duplicate") ||
               message.toLowerCase().contains("connection refused") ||
               message.toLowerCase().contains("connection timeout");
    }
    
    /**
     * 获取数据库异常建议
     */
    private static String getDatabaseSuggestion(String exceptionName, String message) {
        String lowerMessage = message.toLowerCase();
        
        if (lowerMessage.contains("connection refused") || lowerMessage.contains("connection timeout")) {
            return "检查数据库服务状态和连接配置";
        }
        if (lowerMessage.contains("access denied") || lowerMessage.contains("authentication")) {
            return "检查数据库用户名密码和权限配置";
        }
        if (lowerMessage.contains("table") && lowerMessage.contains("doesn't exist")) {
            return "检查表名是否正确或表是否存在";
        }
        if (lowerMessage.contains("column") && lowerMessage.contains("doesn't exist")) {
            return "检查字段名是否正确或字段是否存在";
        }
        if (lowerMessage.contains("duplicate entry")) {
            return "检查唯一约束，避免重复数据插入";
        }
        if (lowerMessage.contains("foreign key constraint")) {
            return "检查外键关联数据是否存在";
        }
        if (lowerMessage.contains("syntax error")) {
            return "检查SQL语句语法是否正确";
        }
        if (lowerMessage.contains("timeout")) {
            return "检查SQL执行超时配置或优化查询";
        }
        
        return "检查数据库连接、SQL语法或权限配置";
    }
    
    /**
     * 判断是否为网络连接相关异常
     */
    private static boolean isNetworkException(String exceptionName, String message) {
        return exceptionName.contains("Connect") ||
               exceptionName.contains("Socket") ||
               exceptionName.contains("Http") ||
               exceptionName.contains("URL") ||
               exceptionName.contains("Timeout") ||
               message.toLowerCase().contains("connection") ||
               message.toLowerCase().contains("timeout") ||
               message.toLowerCase().contains("network") ||
               message.toLowerCase().contains("unreachable") ||
               message.toLowerCase().contains("refused");
    }
    
    /**
     * 获取网络异常建议
     */
    private static String getNetworkSuggestion(String exceptionName, String message) {
        String lowerMessage = message.toLowerCase();
        
        if (lowerMessage.contains("connection refused")) {
            return "检查目标服务是否启动或端口是否正确";
        }
        if (lowerMessage.contains("connection timeout")) {
            return "检查网络连接或增加超时时间配置";
        }
        if (lowerMessage.contains("unreachable")) {
            return "检查网络连通性或目标地址是否正确";
        }
        if (lowerMessage.contains("ssl") || lowerMessage.contains("tls")) {
            return "检查SSL证书配置或HTTPS设置";
        }
        if (lowerMessage.contains("proxy")) {
            return "检查代理服务器配置";
        }
        
        return "检查网络连接、服务状态或超时配置";
    }
    
    /**
     * 判断是否为空指针或数组相关异常
     */
    private static boolean isNullPointerOrArrayException(String exceptionName, String message) {
        return exceptionName.contains("NullPointer") ||
               exceptionName.contains("IndexOutOfBounds") ||
               exceptionName.contains("ArrayIndexOutOfBounds") ||
               exceptionName.contains("StringIndexOutOfBounds") ||
               exceptionName.contains("NegativeArraySize") ||
               message.toLowerCase().contains("null") ||
               message.toLowerCase().contains("index") ||
               message.toLowerCase().contains("bound");
    }
    
    /**
     * 获取空指针或数组异常建议
     */
    private static String getNullPointerOrArraySuggestion(String exceptionName, String message) {
        if (exceptionName.contains("NullPointer")) {
            return "检查对象初始化，添加空值判断";
        }
        if (exceptionName.contains("IndexOutOfBounds") || exceptionName.contains("ArrayIndexOutOfBounds")) {
            return "检查数组边界，确保索引在有效范围内";
        }
        if (exceptionName.contains("StringIndexOutOfBounds")) {
            return "检查字符串操作，确保索引不越界";
        }
        if (exceptionName.contains("NegativeArraySize")) {
            return "检查数组大小，确保不为负数";
        }
        
        return "检查对象初始化和数组边界";
    }
    
    /**
     * 判断是否为类型转换相关异常
     */
    private static boolean isTypeConversionException(String exceptionName, String message) {
        return exceptionName.contains("ClassCast") ||
               exceptionName.contains("NumberFormat") ||
               exceptionName.contains("IllegalArgumentException") ||
               message.toLowerCase().contains("cast") ||
               message.toLowerCase().contains("number format") ||
               message.toLowerCase().contains("cannot be cast");
    }
    
    /**
     * 获取类型转换异常建议
     */
    private static String getTypeConversionSuggestion(String exceptionName, String message) {
        if (exceptionName.contains("ClassCast")) {
            return "检查对象类型，确保类型转换正确";
        }
        if (exceptionName.contains("NumberFormat")) {
            return "检查数字格式，确保输入为有效数字";
        }
        if (message.toLowerCase().contains("cannot be cast")) {
            return "检查对象类型匹配，避免强制类型转换";
        }
        
        return "检查数据类型和转换逻辑";
    }
    
    /**
     * 判断是否为文件操作相关异常
     */
    private static boolean isFileOperationException(String exceptionName, String message) {
        return exceptionName.contains("FileNotFound") ||
               exceptionName.contains("IOException") ||
               exceptionName.contains("FileSystem") ||
               exceptionName.contains("AccessDenied") ||
               message.toLowerCase().contains("file") ||
               message.toLowerCase().contains("directory") ||
               message.toLowerCase().contains("path") ||
               message.toLowerCase().contains("permission denied");
    }
    
    /**
     * 获取文件操作异常建议
     */
    private static String getFileOperationSuggestion(String exceptionName, String message) {
        String lowerMessage = message.toLowerCase();
        
        if (exceptionName.contains("FileNotFound")) {
            return "检查文件路径是否正确或文件是否存在";
        }
        if (lowerMessage.contains("permission denied")) {
            return "检查文件权限，确保有读写权限";
        }
        if (lowerMessage.contains("directory")) {
            return "检查目录是否存在或路径是否正确";
        }
        if (lowerMessage.contains("disk full")) {
            return "检查磁盘空间是否充足";
        }
        
        return "检查文件路径、权限或磁盘空间";
    }
    
    /**
     * 判断是否为权限相关异常
     */
    private static boolean isPermissionException(String exceptionName, String message) {
        return exceptionName.contains("Security") ||
               exceptionName.contains("AccessDenied") ||
               exceptionName.contains("Unauthorized") ||
               exceptionName.contains("Forbidden") ||
               message.toLowerCase().contains("permission") ||
               message.toLowerCase().contains("access denied") ||
               message.toLowerCase().contains("unauthorized") ||
               message.toLowerCase().contains("forbidden");
    }
    
    /**
     * 获取权限异常建议
     */
    private static String getPermissionSuggestion(String exceptionName, String message) {
        String lowerMessage = message.toLowerCase();
        
        if (lowerMessage.contains("unauthorized") || lowerMessage.contains("401")) {
            return "检查用户认证状态和token有效性";
        }
        if (lowerMessage.contains("forbidden") || lowerMessage.contains("403")) {
            return "检查用户权限配置和角色分配";
        }
        if (lowerMessage.contains("access denied")) {
            return "检查访问权限配置";
        }
        
        return "检查用户认证和权限配置";
    }
    
    /**
     * 判断是否为业务逻辑相关异常
     */
    private static boolean isBusinessException(String exceptionName, String message) {
        return exceptionName.contains("Business") ||
               exceptionName.contains("Validation") ||
               exceptionName.contains("IllegalState") ||
               exceptionName.contains("DuplicateRecord") ||
               exceptionName.contains("AntiCheat") ||
               message.toLowerCase().contains("business") ||
               message.toLowerCase().contains("validation") ||
               message.toLowerCase().contains("duplicate") ||
               message.toLowerCase().contains("invalid");
    }
    
    /**
     * 获取业务异常建议
     */
    private static String getBusinessSuggestion(String exceptionName, String message) {
        String lowerMessage = message.toLowerCase();
        
        if (lowerMessage.contains("validation")) {
            return "检查输入数据格式和业务规则";
        }
        if (lowerMessage.contains("duplicate")) {
            return "检查数据唯一性约束";
        }
        if (lowerMessage.contains("invalid")) {
            return "检查输入参数的有效性";
        }
        if (lowerMessage.contains("state")) {
            return "检查业务状态是否满足操作条件";
        }
        
        return "检查业务逻辑和数据验证";
    }
    
    /**
     * 判断是否为Spring框架相关异常
     */
    private static boolean isSpringException(String exceptionName, String message) {
        return exceptionName.contains("Bean") ||
               exceptionName.contains("ApplicationContext") ||
               exceptionName.contains("NoSuchBean") ||
               exceptionName.contains("Circular") ||
               exceptionName.contains("Transaction") ||
               message.toLowerCase().contains("bean") ||
               message.toLowerCase().contains("application context") ||
               message.toLowerCase().contains("circular") ||
               message.toLowerCase().contains("transaction");
    }
    
    /**
     * 获取Spring异常建议
     */
    private static String getSpringSuggestion(String exceptionName, String message) {
        String lowerMessage = message.toLowerCase();
        
        if (lowerMessage.contains("no such bean")) {
            return "检查Bean定义和依赖注入配置";
        }
        if (lowerMessage.contains("circular")) {
            return "检查循环依赖，调整Bean依赖关系";
        }
        if (lowerMessage.contains("transaction")) {
            return "检查事务配置和数据库连接";
        }
        if (lowerMessage.contains("application context")) {
            return "检查Spring配置文件和Bean定义";
        }
        
        return "检查Spring配置和Bean依赖";
    }
    
    /**
     * 判断是否为缓存相关异常
     */
    private static boolean isCacheException(String exceptionName, String message) {
        return exceptionName.contains("Cache") ||
               exceptionName.contains("Redis") ||
               exceptionName.contains("Memcached") ||
               message.toLowerCase().contains("cache") ||
               message.toLowerCase().contains("redis") ||
               message.toLowerCase().contains("memcached");
    }
    
    /**
     * 获取缓存异常建议
     */
    private static String getCacheSuggestion(String exceptionName, String message) {
        String lowerMessage = message.toLowerCase();
        
        if (lowerMessage.contains("connection")) {
            return "检查缓存服务连接状态";
        }
        if (lowerMessage.contains("timeout")) {
            return "检查缓存服务响应时间";
        }
        if (lowerMessage.contains("serialization")) {
            return "检查缓存对象序列化配置";
        }
        
        return "检查缓存服务状态和配置";
    }
    
    /**
     * 判断是否为消息队列相关异常
     */
    private static boolean isMessageQueueException(String exceptionName, String message) {
        return exceptionName.contains("Kafka") ||
               exceptionName.contains("RabbitMQ") ||
               exceptionName.contains("ActiveMQ") ||
               exceptionName.contains("Message") ||
               message.toLowerCase().contains("kafka") ||
               message.toLowerCase().contains("rabbit") ||
               message.toLowerCase().contains("activemq") ||
               message.toLowerCase().contains("queue");
    }
    
    /**
     * 获取消息队列异常建议
     */
    private static String getMessageQueueSuggestion(String exceptionName, String message) {
        String lowerMessage = message.toLowerCase();
        
        if (lowerMessage.contains("connection")) {
            return "检查消息队列服务连接状态";
        }
        if (lowerMessage.contains("topic") || lowerMessage.contains("queue")) {
            return "检查主题或队列是否存在";
        }
        if (lowerMessage.contains("consumer")) {
            return "检查消费者配置和状态";
        }
        if (lowerMessage.contains("producer")) {
            return "检查生产者配置和连接";
        }
        
        return "检查消息队列服务状态和配置";
    }
    
    /**
     * 判断是否为数据库操作
     */
    private static boolean isDatabaseOperation(String className, String methodName) {
        return className.contains("Mapper") ||
               className.contains("Repository") ||
               className.contains("Dao") ||
               className.contains("JdbcTemplate") ||
               className.contains("MyBatis") ||
               className.contains("Hibernate") ||
               methodName.contains("select") ||
               methodName.contains("insert") ||
               methodName.contains("update") ||
               methodName.contains("delete") ||
               methodName.contains("query") ||
               methodName.contains("execute");
    }
    
    /**
     * 获取数据库操作建议
     */
    private static String getDatabaseOperationSuggestion(String className, String methodName, String fileName, int lineNumber) {
        if (methodName.contains("select") || methodName.contains("query")) {
            return "检查查询SQL语法和数据库连接状态";
        }
        if (methodName.contains("insert")) {
            return "检查插入数据格式和唯一约束";
        }
        if (methodName.contains("update")) {
            return "检查更新条件和数据有效性";
        }
        if (methodName.contains("delete")) {
            return "检查删除条件和外键约束";
        }
        if (className.contains("Mapper")) {
            return "检查MyBatis映射文件配置";
        }
        
        return "检查数据库操作和SQL语句";
    }
    
    /**
     * 判断是否为HTTP操作
     */
    private static boolean isHttpOperation(String className, String methodName) {
        return className.contains("RestTemplate") ||
               className.contains("WebClient") ||
               className.contains("HttpClient") ||
               className.contains("Feign") ||
               className.contains("OkHttp") ||
               methodName.contains("get") ||
               methodName.contains("post") ||
               methodName.contains("put") ||
               methodName.contains("delete") ||
               methodName.contains("exchange") ||
               methodName.contains("execute");
    }
    
    /**
     * 获取HTTP操作建议
     */
    private static String getHttpOperationSuggestion(String className, String methodName, String fileName, int lineNumber) {
        if (methodName.contains("get")) {
            return "检查GET请求URL和参数格式";
        }
        if (methodName.contains("post")) {
            return "检查POST请求体和Content-Type";
        }
        if (methodName.contains("exchange")) {
            return "检查HTTP请求配置和响应处理";
        }
        if (className.contains("RestTemplate")) {
            return "检查RestTemplate配置和连接池";
        }
        if (className.contains("Feign")) {
            return "检查Feign客户端配置";
        }
        
        return "检查HTTP请求配置和网络连接";
    }
    
    /**
     * 判断是否为文件操作
     */
    private static boolean isFileOperation(String className, String methodName) {
        return className.contains("File") ||
               className.contains("Path") ||
               className.contains("Files") ||
               className.contains("InputStream") ||
               className.contains("OutputStream") ||
               className.contains("Reader") ||
               className.contains("Writer") ||
               methodName.contains("read") ||
               methodName.contains("write") ||
               methodName.contains("copy") ||
               methodName.contains("move") ||
               methodName.contains("delete");
    }
    
    /**
     * 获取文件操作建议
     */
    private static String getFileOperationSuggestion(String className, String methodName, String fileName, int lineNumber) {
        if (methodName.contains("read")) {
            return "检查文件路径和读取权限";
        }
        if (methodName.contains("write")) {
            return "检查文件路径和写入权限";
        }
        if (methodName.contains("copy") || methodName.contains("move")) {
            return "检查源文件和目标路径";
        }
        if (methodName.contains("delete")) {
            return "检查文件存在性和删除权限";
        }
        if (className.contains("InputStream") || className.contains("OutputStream")) {
            return "检查流操作和资源关闭";
        }
        
        return "检查文件路径、权限和磁盘空间";
    }
    
    /**
     * 判断是否为缓存操作
     */
    private static boolean isCacheOperation(String className, String methodName) {
        return className.contains("Cache") ||
               className.contains("Redis") ||
               className.contains("Memcached") ||
               className.contains("Caffeine") ||
               methodName.contains("get") ||
               methodName.contains("put") ||
               methodName.contains("set") ||
               methodName.contains("delete") ||
               methodName.contains("evict") ||
               methodName.contains("clear");
    }
    
    /**
     * 获取缓存操作建议
     */
    private static String getCacheOperationSuggestion(String className, String methodName, String fileName, int lineNumber) {
        if (methodName.contains("get")) {
            return "检查缓存键名和序列化配置";
        }
        if (methodName.contains("put") || methodName.contains("set")) {
            return "检查缓存键值和过期时间";
        }
        if (methodName.contains("delete") || methodName.contains("evict")) {
            return "检查缓存键名和删除权限";
        }
        if (className.contains("Redis")) {
            return "检查Redis连接和配置";
        }
        if (className.contains("Caffeine")) {
            return "检查本地缓存配置";
        }
        
        return "检查缓存服务状态和配置";
    }
    
    /**
     * 判断是否为消息队列操作
     */
    private static boolean isMessageQueueOperation(String className, String methodName) {
        return className.contains("Kafka") ||
               className.contains("RabbitMQ") ||
               className.contains("ActiveMQ") ||
               className.contains("Producer") ||
               className.contains("Consumer") ||
               methodName.contains("send") ||
               methodName.contains("publish") ||
               methodName.contains("consume") ||
               methodName.contains("poll") ||
               methodName.contains("commit");
    }
    
    /**
     * 获取消息队列操作建议
     */
    private static String getMessageQueueOperationSuggestion(String className, String methodName, String fileName, int lineNumber) {
        if (methodName.contains("send") || methodName.contains("publish")) {
            return "检查消息格式和主题配置";
        }
        if (methodName.contains("consume") || methodName.contains("poll")) {
            return "检查消费者配置和消息处理";
        }
        if (methodName.contains("commit")) {
            return "检查事务提交和偏移量管理";
        }
        if (className.contains("Kafka")) {
            return "检查Kafka连接和主题配置";
        }
        if (className.contains("RabbitMQ")) {
            return "检查RabbitMQ连接和队列配置";
        }
        
        return "检查消息队列服务状态和配置";
    }
    
    /**
     * 判断是否为业务操作
     */
    private static boolean isBusinessOperation(String className, String methodName) {
        return className.contains("Service") ||
               className.contains("Controller") ||
               className.contains("Manager") ||
               className.contains("Handler") ||
               className.contains("Processor") ||
               methodName.contains("save") ||
               methodName.contains("update") ||
               methodName.contains("delete") ||
               methodName.contains("validate") ||
               methodName.contains("process");
    }
    
    /**
     * 获取业务操作建议
     */
    private static String getBusinessOperationSuggestion(String className, String methodName, String fileName, int lineNumber) {
        if (methodName.contains("save")) {
            return "检查保存数据的完整性和约束";
        }
        if (methodName.contains("update")) {
            return "检查更新条件和数据有效性";
        }
        if (methodName.contains("delete")) {
            return "检查删除条件和业务约束";
        }
        if (methodName.contains("validate")) {
            return "检查输入数据验证规则";
        }
        if (methodName.contains("process")) {
            return "检查业务处理逻辑和状态";
        }
        if (className.contains("Controller")) {
            return "检查请求参数和响应处理";
        }
        if (className.contains("Service")) {
            return "检查业务逻辑和事务配置";
        }
        
        return "检查业务逻辑和数据验证";
    }
    
    /**
     * 获取环境显示名称
     */
    public static String getEnvironmentDisplayName(String env) {
        if (env == null) {
            return "未知环境";
        }
        
        switch (env.toLowerCase()) {
            case "dev":
                return "开发环境";
            case "test":
                return "测试环境";
            case "uat":
                return "UAT环境";
            case "prod":
                return "生产环境";
            default:
                return env + "环境";
        }
    }
} 