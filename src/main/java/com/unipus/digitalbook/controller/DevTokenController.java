package com.unipus.digitalbook.controller;

import com.unipus.digitalbook.common.exception.business.BizException;
import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.common.utils.JwtUtil;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.constants.CacheConstant;
import com.unipus.digitalbook.model.constants.WebConstant;
import com.unipus.digitalbook.model.entity.CurrentUserInfo;
import com.unipus.digitalbook.model.enums.ResultMessage;
import com.unipus.digitalbook.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 开发测试环境专用Token控制器
 * 仅在非生产环境启用
 */
@Tag(name = "开发测试Token", description = "仅限开发测试环境使用的Token生成接口")
@RestController
@RequestMapping("/auth")
@Slf4j
public class DevTokenController {

    @Resource
    private Environment environment;
    
    @Resource
    private JwtUtil jwtUtil;
    
    @Resource
    private UserService userService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Value("${dev.token.secret-keys:devKey}")
    private List<String> devSecretKeys;

    /**
     * 生成开发测试环境专用token
     */
    @GetMapping("/accessToken")
    @Operation(summary = "获取开发测试Token", description = "仅限开发测试环境使用，需要提供开发密钥和通过IP白名单验证")
    public Response<HashMap<String, Object>> generateDevToken(
            @RequestParam(value = "userId") Long userId,
            @RequestParam(value = "orgId") Long orgId,
            @RequestParam(value = "devKey") String devKey,
            HttpServletRequest request) {

        // 1. 请求频率限制
        rateLimitService(request);
        // 2. 环境安全检查
        validateEnvironment();
        // 3. 开发密钥验证
        validateDevKey(devKey);
        // 4. 用户信息验证
        CurrentUserInfo currentUserInfo = validateUser(userId, orgId);
        // 5. 生成安全token
        String token = generateSecureToken(currentUserInfo, userId);
        // 6. 构建响应
        return Response.success("开发Token生成成功", buildTokenResponse(token));
    }

    /**
     * 验证环境是否允许访问
     */
    private void validateEnvironment() {
        List<String> allowedProfiles = List.of("local", "dev", "test");
        List<String> activeProfiles = List.of(environment.getActiveProfiles());
        
        if (!CollectionUtils.containsAny(activeProfiles, allowedProfiles)) {
            throw new BizException(ResultMessage.SYS_ONLY_DEV_ENV_USE_ERROR, "当前环境不允许使用开发Token接口");
        }
    }

    /**
     * 验证用户信息
     */
    private CurrentUserInfo validateUser(Long userId, Long orgId) {
        CurrentUserInfo currentUserInfo = userService.getCurrentUserInfo(userId, orgId);
        if (currentUserInfo == null) {
            throw new BizException(ResultMessage.SYS_ONLY_DEV_ENV_USE_ERROR, String.format("用户不存在: userId=%d, orgId=%d", userId, orgId));
        }
        return currentUserInfo;
    }

    /**
     * 请求频率限制服务
     */
    private void rateLimitService(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }

        String rateLimitKey = "rate_limit:" + ip;
        Long count = stringRedisTemplate.opsForValue().increment(rateLimitKey);
        if (count != null && count > 10) {  // 每分钟最多 10 次
            log.warn("请求频率过高，IP: {}", ip);
            throw new BizException("请求频率过高");
        }
        stringRedisTemplate.expire(rateLimitKey, 1, TimeUnit.MINUTES);
    }

    /**
     * 验证开发密钥
     */
    private void validateDevKey(String devKey) {
        if (!devSecretKeys.contains(devKey)) {
            log.warn("无效的开发密钥: {}", devKey);
            throw new BizException("无效的开发密钥");
        }
    }

    /**
     * 生成安全的token
     */
    private String generateSecureToken(CurrentUserInfo currentUserInfo, Long userId) {
        // 生成短期serviceTicket
        String serviceTicket = UUID.randomUUID().toString();
        int tokenValidHours = 2; // 2小时有效期

        // 缓存serviceTicket
        stringRedisTemplate.opsForValue().set(
            CacheConstant.REDIS_AUTH_PREFIX + userId,
            serviceTicket,
            tokenValidHours,
            TimeUnit.HOURS
        );

        // 缓存用户信息
        stringRedisTemplate.opsForValue().set(
            CacheConstant.REDIS_USR_PREFIX + userId,
            JsonUtil.toJsonString(currentUserInfo),
            tokenValidHours,
            TimeUnit.HOURS
        );

        // 构建JWT claims
        Map<String, Object> claims = new HashMap<>();
        claims.put(WebConstant.JWT_SERVICE_TICKET, serviceTicket);
        claims.put(WebConstant.JWT_USER_ID, userId);
        claims.put("dev_token", true);
        claims.put("iat", Instant.now().getEpochSecond());
        claims.put("exp", Instant.now().plusSeconds(2 * 3600).getEpochSecond());
        claims.put("iss", "digital-book-service");
        claims.put("aud", "dev-token");
        claims.put("env", String.join(",", environment.getActiveProfiles()));

        return jwtUtil.generateToken(claims, currentUserInfo.getUserInfo().getCellPhone());
    }

    /**
     * 构建token响应
     */
    private HashMap<String, Object> buildTokenResponse(String token) {
        HashMap<String, Object> response = new HashMap<>();
        response.put("access_token", token);
        response.put("token_type", "Bearer");
        response.put("expires_in", 24*3600); // 24小时，单位：秒
        response.put("environment", String.join(",", environment.getActiveProfiles()));
        response.put("issued_at", System.currentTimeMillis());
        response.put("usage", "development_only");
        return response;
    }

}