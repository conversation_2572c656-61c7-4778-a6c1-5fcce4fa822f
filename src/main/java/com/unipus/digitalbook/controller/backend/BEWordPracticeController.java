package com.unipus.digitalbook.controller.backend;

import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.wordpractice.WordPracticeListDTO;
import com.unipus.digitalbook.service.WordPracticeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "", description = "")
@RestController
@RequestMapping("/backend/wordpractice")
@Slf4j
public class BEWordPracticeController {

    @Resource
    private WordPracticeService wordPracticeService;

    @GetMapping("/getBookAllWordPractice")
    @Operation(summary = "根据教材ID获取教材所有词汇学练", description = "根据教材ID获取教材所有词汇学练")
    public Response<WordPracticeListDTO> getBookAllWordPractice(@RequestParam("bookId") String bookId) {
        return Response.success(wordPracticeService.getBookAllWordPractice(bookId));
    }
}
