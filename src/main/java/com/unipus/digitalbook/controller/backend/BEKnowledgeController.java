package com.unipus.digitalbook.controller.backend;

import com.unipus.digitalbook.controller.BaseController;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.knowledge.BookKnowledgeInfoGetDTO;
import com.unipus.digitalbook.model.params.knowledge.KnowledgeSourceDeleteByThirdIdsParam;
import com.unipus.digitalbook.service.KnowledgeResourceService;
import com.unipus.digitalbook.service.KnowledgeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/backend/knowledge")
@Tag(name = "教材知识图谱相关功能", description = "为了权限控制，该控制器特意未后端服务提供接口")
public class BEKnowledgeController extends BaseController {

    @Resource
    private KnowledgeResourceService knowledgeResourceService;

    @Resource
    private KnowledgeService knowledgeService;

    /**
     * 删除三方资源的关系
     *
     * @param param
     * @return 基础响应
     */
    @PostMapping("/resource/batchDelete")
    @Tag(name = "教材标注资源批量删除", description = "教材标注资源批量删除")
    @Operation(summary = "三方知识点删除，孤立教材资源的标注信息，也需要批量删除", description = "删除三方资源相关信息")
    public Response sourceDeleteByThirdIds(@RequestBody KnowledgeSourceDeleteByThirdIdsParam param) {
        knowledgeResourceService.knowledgeResourceDeleteByThirdIds(param, getCurrentUserId());
        return Response.success();
    }

    @GetMapping("/course/get")
    @Operation(summary = "查询教材的知识图谱",
            description = "查询知识图谱",
            method = "Get"
    )
    public Response<BookKnowledgeInfoGetDTO> courseKnowledgeInfoGet(@RequestParam("courseIdStr") String bookId,
                                                                    @RequestParam(required = false, defaultValue = "") Integer status) {
        return Response.success(knowledgeService.courseKnowledgeInfoGet(bookId, status));
    }

}
