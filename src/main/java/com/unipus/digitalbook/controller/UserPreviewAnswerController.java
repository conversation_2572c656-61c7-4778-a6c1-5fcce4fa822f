package com.unipus.digitalbook.controller;


import com.unipus.digitalbook.common.utils.ScoreUtil;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.question.*;
import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.entity.question.UserAnswerList;
import com.unipus.digitalbook.model.params.question.PreviewAnswerScoreParam;
import com.unipus.digitalbook.model.params.question.UserAnswerListParam;
import com.unipus.digitalbook.service.UserAnswerService;
import com.unipus.digitalbook.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 预览作答接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/answer/preview")
@Tag(name = "预览作答接口相关功能", description = "作答接口相关接口")
public class UserPreviewAnswerController extends BaseController {
    @Resource
    private UserAnswerService userAnswerService;
    @Resource
    private UserService userService;
    @PostMapping("/judgeStart")
    @Operation(summary = "预览作答开始进行评测", description = "提交用户答案进行评测，只用于异步作答场景", method = "POST")
    public Response<JudgeTaskListDTO> judgeStart(@RequestBody PreviewAnswerScoreParam request) {
        Long currentUserId = getCurrentUserId();
        String ssoId = userService.getUserInfo(currentUserId).getSsoId();
        BigQuestionGroup group = request.toQuestionEntity(currentUserId);
        UserAnswerList userAnswerList = request.toUserAnswerEntity(ssoId);
        List<JudgeTaskDTO> judgeTaskList = userAnswerService.judgeStart(group, userAnswerList)
                .stream().map(JudgeTaskDTO::new).toList();
        return Response.success(new JudgeTaskListDTO(judgeTaskList));
    }

    @PostMapping("/judgeResult")
    @Operation(summary = "预览作答获取评测结果", description = "获取评测结果，只用于异步作答场景 获取judgeStart的结果", method = "POST")
    public Response<UserAnswerResponseDTO> fetchJudgeResult(@RequestBody UserAnswerListParam request) {
        // 预览使用默认版本
        Long currentUserId = getCurrentUserId();
        // 根据用户id获取openId
        String ssoId = userService.getUserInfo(currentUserId).getSsoId();
        UserAnswerList userAnswerList = request.toEntity(ssoId);
        List<UserAnswerResultDTO> userAnswersResult = userAnswerService.fetchJudgeResult(userAnswerList).getUserAnswers()
                .stream().map(UserAnswerResultDTO::new).collect(Collectors.toList());
        return Response.success(new UserAnswerResponseDTO(userAnswersResult));
    }

    @PostMapping("/submitScore")
    @Operation(summary = "预览作答用户进行判分", description = "提交用户答案进行判分，所有题型通用", method = "POST")
    public Response<UserAnswerResponseDTO> submitScore(@RequestBody PreviewAnswerScoreParam request) {
        // 预览使用默认版本
        Long currentUserId = getCurrentUserId();
        String ssoId = userService.getUserInfo(currentUserId).getSsoId();
        // 构建题组
        BigQuestionGroup group = request.toQuestionEntity(currentUserId);
        UserAnswerList userAnswerList = request.toUserAnswerEntity(ssoId);
        UserAnswerResponseDTO userAnswerResponseDTO = new UserAnswerResponseDTO();
        // 当前题组总分
        UserAnswerList resultAnswerList = userAnswerService.judgeScore(group, userAnswerList);
        // 返回用户答题结果
        List<UserAnswerResultDTO> userAnswerResultList = resultAnswerList.getUserAnswers().stream().map(UserAnswerResultDTO::new).toList();
        userAnswerResponseDTO.setScore(ScoreUtil.keepTwoDecimal(resultAnswerList.getScore()));
        userAnswerResponseDTO.setCorrectAnswers(AnswerDTO.toDTOList(group.fetchCorrectAnswers()));
        userAnswerResponseDTO.setUserAnswersResult(userAnswerResultList);
        return Response.success(userAnswerResponseDTO);
    }

    @GetMapping("/getAnswerCallbackEvaluation")
    @Operation(summary = "获取用户评测结果", description = "根据作答业务ID获取用户评测结果", method = "GET")
    public Response<GetUserAnswerEvaluationDTO> getAnswerCallbackEvaluation(@RequestParam String bizAnswerId) {
        String evaluation = userAnswerService.getAnswerCallbackEvaluation(bizAnswerId);
        return Response.success(new GetUserAnswerEvaluationDTO(bizAnswerId, evaluation));
    }
}
