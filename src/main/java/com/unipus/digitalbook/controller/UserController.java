package com.unipus.digitalbook.controller;

import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.user.*;
import com.unipus.digitalbook.model.entity.UserInfo;
import com.unipus.digitalbook.model.entity.user.SearchUserList;
import com.unipus.digitalbook.model.entity.user.UserImportResult;
import com.unipus.digitalbook.model.params.user.*;
import com.unipus.digitalbook.service.UserImportService;
import com.unipus.digitalbook.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@RestController
@RequestMapping("/user")
@Tag(name = "用户相关功能", description = "用户相关接口")
public class UserController extends BaseController {
    @Resource
    private UserService userService;
    @Resource
    private UserImportService userImportService;

    @PostMapping("/searchUserListForAmin")
    @Operation(summary = "查询用户列表",
            description = "查询用户列表，根据手机号，组织，激活状态联合查询",
            method = "POST"
    )
    public Response<SearchUserListDTO> searchUserList(@RequestBody SearchUserParam param) {
        SearchUserList searchUserList = userService.searchUserList(
                param.getOrgId(), param.getCellPhone(), param.getStatus(), param.getUserName(), param.getPageParams());
        return Response.success(new SearchUserListDTO(searchUserList));
    }


//    @GetMapping("/searchUserListForAmin")
//    @Operation(summary = "查询用户列表",
//            description = "用户查询，根据手机号，组织，激活状态联合查询",
//            method = "GET"
//    )
//    public Response<UserListDTO> searchUserListForAmin(@RequestBody SearchAdminParam param) {
//
//        List<UserInfo> userList = userService.searchByPhoneAndOrgAndStatus(param.getCellPhone(), param.getOrgId(), param.getActive(),param.getPageParams());
//        Map<Long,List<Organization>> userId2OrgList=orgService.;
//        Map<Long,List<Role>> userId2RoleList=roleService.;
//
//
//        return null;
//    }


    @GetMapping("/getUserByCellPhone")
    @Operation(summary = "查询用户",
            description = "查询用户，根据手机号查询",
            method = "GET"
    )
    public Response<UserInfoDTO> getUserByCellPhone(@RequestParam("cellPhone") String cellPhone) {
        UserInfo userInfo = userService.getUserInfoByCellPhone(cellPhone);
        return Response.success(new UserInfoDTO(userInfo));
    }

    @Operation(summary = "新建用户",
            description = "新建用户",
            method = "POST"
    )
    @PostMapping("/addUser")
    public Response<Boolean> addUser(@RequestBody AddUserParam addUserParam) {
        Boolean result = userService.addUser(addUserParam.toEntity(), addUserParam.getOrganizationId(), addUserParam.getRoleIds(), BaseController.getCurrentUserId());
        if (result) {
            return Response.success("新增成功", true);
        }
        return Response.fail();
    }

    @Operation(summary = "编辑用户",
            description = "超管或机构管理员在后台管理中，编辑未激活的用户信息",
            method = "POST"
    )
    @PostMapping("/editUser4Admin")
    public Response<Boolean> editUser4Admin(@RequestBody EditUser4AdminParam addUserParam) {
        Boolean result = userService.editUser4Admin(addUserParam.toEntity(), BaseController.getCurrentUserId());
        if (result) {
            return Response.success("编辑成功", true);
        }
        return Response.fail();
    }

    @PostMapping("/downloadTemplate")
    @Operation(summary = "下载Excel用户导入模板",
            description = "下载Excel用户导入模板，后面通过Excel，导入用户",
            method = "POST"
    )
    public void downloadTemplate(@RequestParam("orgId") Long orgId, HttpServletResponse response) {
        userImportService.downloadTemplate(orgId, response);
    }

    @PostMapping(value ="/importByExcel", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "通过Excel，导入用户",
            description = "通过Excel，导入用户，返回任务id，后面根据任务ID查询进度和结果",
            method = "POST"
    )
    public Response<ImportTaskIdDTO> importByExcel(@RequestParam("orgId") Long orgId,
                                                   @Parameter(description = "Excel文件", required = true, content = @Content(mediaType = MediaType.MULTIPART_FORM_DATA_VALUE))
                                                   @RequestPart("file") MultipartFile file) {
        Long taskId = userImportService.importByExcel(getCurrentUserId(), orgId, file);
        return Response.success(new ImportTaskIdDTO(taskId));
    }

    @GetMapping("/getImportResult")
    @Operation(summary = "导入用户的结果查询",
            description = "根据任务ID查询进度和结果",
            method = "GET"
    )
    public Response<ImportResultDTO> getImportResult(@RequestParam("taskId") Long taskId) {
        UserImportResult userImportResult = userImportService.getById(taskId);
        return Response.success(new ImportResultDTO(userImportResult));
    }



    // 获取当前用户基本信息
    @GetMapping("/getUserBaseInfo")
    @Operation(summary = "获取当前用户基本信息", description = "获取当前用户基本信息", method = "GET")
    public Response<UserInfoDTO> getUserBaseInfo() {
        UserInfoDTO userInfo = new UserInfoDTO(userService.getUserInfo(getCurrentUserId()));
        return Response.success(userInfo);
    }

    // 获取当前用户信息
    @GetMapping("/getCurrentUserInfo")
    @Operation(summary = "获取当前用户信息", description = "获取当前用户信息", method = "GET")
    public Response<CurrentUserDTO> getCurrentUserInfo() {
        CurrentUserDTO userInfo = CurrentUserDTO.build(userService.getCurrentUserInfo(getCurrentUserId(), getCurrentOrgId()));
        return Response.success(userInfo);
    }

    // 更新当前用户名称
    @PostMapping("/updateCurrentUserInfo")
    @Operation(summary = "更新当前用户名称", description = "更新当前用户名称", method = "POST")
    public Response<Boolean> updateCurrentUserInfo(@RequestBody UpdateUserParam param) {
        return Response.success(userService.updateCurrentUserInfo(param.toEntities(), getCurrentUserId()));
    }

    @GetMapping("/getUserInfo")
    @Operation(summary = "获取用户角色信息", description = "获取用户角色信息", method = "GET")
    public Response<UserInfoDTO> getUserInfoById(@RequestParam("userId") Long userId) {
        UserInfoDTO userInfo = new UserInfoDTO(userService.getUserInfo(userId));
        return Response.success(userInfo);
    }

    @PostMapping("/getUserListByKeyword")
    @Operation(summary = "根据关键字查询用户列表", description = "通过关键字模糊匹配用户昵称或精确匹配手机号查询用户列表", method = "POST")
    public Response<SimpleUserListDTO> getUserListByKeyword(@RequestBody KeywordSearchParam param) {
        List<UserInfo> userInfos = userService.selectValidUsersByOrgAndKeyword(param.getOrgId(), param.getKeyword(), param.getPageParams());
        return Response.success(SimpleUserListDTO.build(userInfos));
    }
}
