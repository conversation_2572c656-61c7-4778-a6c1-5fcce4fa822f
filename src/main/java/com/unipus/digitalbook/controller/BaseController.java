package com.unipus.digitalbook.controller;

import com.unipus.digitalbook.common.utils.IpAddressUtil;
import com.unipus.digitalbook.model.constants.WebConstant;
import com.unipus.digitalbook.model.entity.UserAccessInfo;
import com.unipus.digitalbook.model.enums.ReaderTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Optional;

@Slf4j
public class BaseController {

    /**
     * 安全地获取当前登录用户ID
     *
     * @return 当前用户ID，如果获取不到则返回null
     */
    public static Long getCurrentUserId() {
        return Optional.ofNullable(RequestContextHolder.getRequestAttributes())
                .map(attributes -> ((ServletRequestAttributes) attributes).getRequest())
                .map(request -> request.getAttribute(WebConstant.JWT_USER_ID))
                .map(userIdObj -> {
                    switch (userIdObj) {
                        case Long id -> {
                            return id;
                        }
                        case Integer intId -> {
                            return intId.longValue();
                        }
                        case String strId -> {
                            return parseToLong(strId);
                        }
                        default -> {
                        }
                    }
                    return null;
                })
                .orElse(null);
    }

    /**
     * 获取当前系统用户ID
     * @return 当前系统用户ID，如果获取不到则返回null
     */
    public static Long getCurrentOrgId() {
        String orgIdStr = getHeaderByName(WebConstant.HEADER_ORG_ID);
        return parseToLong(orgIdStr);
    }

    /**
     * 应用侧：内部用户ID
     * @return 内部用户ID，如果获取不到则返回null
     */
    public static String getOpenId(){
        return getRequestAttributeByName(WebConstant.JWT_BACKEND_OID);
    }

    /**
     * 应用侧：读者类型
     * @return 读者类型枚举，如果获取不到则返回null
     */
    public static ReaderTypeEnum getReaderType(){
        String readerTypeCode = getRequestAttributeByName(WebConstant.JWT_BACKEND_RID);
        return readerTypeCode==null ? null : ReaderTypeEnum.getByCode(Integer.valueOf(readerTypeCode));
    }

    /**
     * 应用侧：租户ID
     * @return 租户ID，如果获取不到则返回null
     */
    public static Long getTenantId() {
        String appIdStr = getRequestAttributeByName(WebConstant.JWT_BACKEND_AID);
        return parseToLong(appIdStr);
    }

    /**
     * 应用侧：用户数据包ID
     * @return 用户数据包ID，如果获取不到则返回null
     */
    public static String getDataPackage(){
        return getRequestAttributeByName(WebConstant.JWT_BACKEND_PID);
    }

    public static String getClientIp() {
        return IpAddressUtil.getLastIpAddress(((ServletRequestAttributes)RequestContextHolder.getRequestAttributes()).getRequest());
    }

    public static String getEnvPartition(){
        return getRequestAttributeByName(WebConstant.JWT_ENV_PID);
    }

    /** 从请求上下文中获取指定名称的属性值 */
    private static String getRequestAttributeByName(String attributeName) {
        return Optional.ofNullable(RequestContextHolder.getRequestAttributes())
                .map(attributes -> ((ServletRequestAttributes) attributes).getRequest())
                .map(request -> request.getAttribute(attributeName))
                .map(Object::toString)
                .orElse(null);
    }

    /** 从请求头中获取指定名称的值 */
    private static String getHeaderByName(String headerName) {
        return Optional.ofNullable(RequestContextHolder.getRequestAttributes())
                .map(attributes -> ((ServletRequestAttributes) attributes).getRequest())
                .map(request -> request.getHeader(headerName))
                .orElse(null);
    }

    /**
     * 将字符串转换为Long类型。
     *
     * @param data 待转换的字符串
     * @return 转换成功后的Long值，如果转换失败则返回null
     */
    private static Long parseToLong(String data) {
        if (data == null || data.trim().isEmpty()) {
            // 如果字符串为空或仅包含空格，则直接返回null，避免转换异常
            return null;
        }
        try {
            return Long.parseLong(data);
        } catch (NumberFormatException e) {
            log.error("无法将字符串转换为Long: {},exception:{}", data, e.getMessage());
            return null;
        }
    }

    /**
     * 获取用户访问信息
     * @return 用户访问信息
     */
    public UserAccessInfo getUserAccessInfo() {
        UserAccessInfo userAccessInfo = new UserAccessInfo();

        if(getEnvPartition()==null) {
            // 系统用户
            userAccessInfo.setOpenId(Optional.ofNullable(getCurrentUserId()).map(Object::toString).orElse(null));
            // todo:平台租户ID将来从组织映射中获取
            userAccessInfo.setTenantId(0L);
        }else{
            // 平台请求用户
            userAccessInfo.setOpenId(getOpenId());
            userAccessInfo.setTenantId(getTenantId());

            userAccessInfo.setDataPackage(getDataPackage());
            userAccessInfo.setClientIp(getClientIp());

            userAccessInfo.setReaderType(getReaderType());
            userAccessInfo.setEnvPartition(getEnvPartition());
        }

        return userAccessInfo;
    }

    /**
     * 获取 Authorization 请求头
     */
    public static String getAuthorizationHeader() {
        return getHeaderByName(WebConstant.HEADER_AUTHORIZATION);
    }

}
