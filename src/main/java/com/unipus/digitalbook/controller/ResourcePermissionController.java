package com.unipus.digitalbook.controller;

import com.unipus.digitalbook.aop.permission.DataPermission;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.DataListDTO;
import com.unipus.digitalbook.model.dto.IdListDTO;
import com.unipus.digitalbook.model.dto.book.BookUserListDTO;
import com.unipus.digitalbook.model.entity.permission.ResourcePermission;
import com.unipus.digitalbook.model.enums.PermissionTypeEnum;
import com.unipus.digitalbook.model.enums.ResourceTypeEnum;
import com.unipus.digitalbook.model.params.book.PermissionSearchParam;
import com.unipus.digitalbook.model.params.book.ResourceUserPermissionParam;
import com.unipus.digitalbook.model.params.book.ResourceUserSearchParam;
import com.unipus.digitalbook.service.ResourcePermissionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 资源权限操作
 */
@RestController
@RequestMapping("/permission")
@Tag(name = "资源权限操作", description = "对资源的权限的分配、查询和移除操作")
public class ResourcePermissionController extends BaseController {

    @Resource
    ResourcePermissionService resourcePermissionService;

    @Operation(summary = "获取平台内所有未分享用户", description = "适用于资源分享，获取平台内所有未分享用户", method = "POST")
    @PostMapping("/getUsersToShare")
    public Response<BookUserListDTO> getUsersToShare(@RequestBody ResourceUserSearchParam param) {
        return Response.success(new BookUserListDTO(resourcePermissionService.getUsersToShare(param)));
    }

    @Operation(summary = "获取平台内所有已分享用户", description = "适用于资源分享，获取平台内所有已分享用户", method = "POST")
    @PostMapping("/getSharedUsers")
    public Response<BookUserListDTO> getSharedUsers(@RequestBody ResourceUserSearchParam param) {
        return Response.success(new BookUserListDTO(resourcePermissionService.getSharedUsers(param)));
    }

    @Operation(summary = "更新用户分享权限", description = "更新用户分享权限", method = "POST")
    @PostMapping("/updateUserSharePermission")
    public Response<Boolean> updateUserSharePermission(@RequestBody ResourceUserPermissionParam param) {
        Boolean result = resourcePermissionService.updateUserSharePermission(param.getResourceId(), param.getUserIds(), getCurrentUserId());
        return Response.success(result? "预览分享成功" : "预览分享失败", result);
    }

    @Operation(summary = "取得当前用户资源列表", description = "取得当前用户资源列表", method = "POST")
    @PostMapping("/getUserResources")
    public Response<IdListDTO<String>> getUserBooks(
            @RequestParam(value = "permissionType", required = false) Integer permissionType,
            @RequestParam(value = "resourceType", required = false) Integer resourceType) {
        return Response.success(new IdListDTO<>(resourcePermissionService.getUserResources(
                getCurrentUserId(), ResourceTypeEnum.getResourceTypeEnum(permissionType),
                PermissionTypeEnum.getPermissionTypeEnum(resourceType))));
    }

    @Operation(summary = "根据资源ID或用户ID获取权限信息列表", description = "根据资源ID或用户ID获取权限信息列表", method = "POST")
    @PostMapping("/getPermissions")
    public Response<DataListDTO<ResourcePermission>> getPermissions(@RequestBody List<PermissionSearchParam> params){
        return Response.success(new DataListDTO<>(resourcePermissionService.getPermissions(params)));
    }

    // todo：测试权限注解，后续需要删除
    @PostMapping("/testReadPermission")
    @DataPermission(resourceId = "#resourcePermissionParam.resourceId", permissionTypes = {PermissionTypeEnum.READ})
    public Response<Boolean> updateReadPermission(@RequestBody ResourceUserPermissionParam resourcePermissionParam) {
        List<ResourcePermission> permissions = resourcePermissionParam.toEntity(ResourceTypeEnum.BOOK, PermissionTypeEnum.READ);
        return Response.success(resourcePermissionService.updateUserPermission(permissions, getCurrentUserId()));
    }
}
