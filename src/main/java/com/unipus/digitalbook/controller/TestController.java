package com.unipus.digitalbook.controller;

import com.unipus.digitalbook.common.utils.BloomFilterUtil;
import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.dao.UserInfoPOMapper;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.po.UserInfoPO;
import com.unipus.digitalbook.service.Cms2QrCodeService;
import com.unipus.digitalbook.service.remote.restful.aigc.LlmService;
import com.unipus.digitalbook.service.remote.restful.aigc.model.AigcApiResponse;
import com.unipus.digitalbook.service.remote.restful.qrcode.QrCodeApiService;
import com.unipus.digitalbook.service.remote.restful.qrcode.model.AddQrCodeRequest;
import com.unipus.digitalbook.service.remote.restful.qrcode.model.QrCodeApiResponse;
import com.unipus.digitalbook.service.remote.restful.qrcode.model.QrCodeInfo;
import com.unipus.digitalbook.service.remote.restful.qrcode.model.UpdateQrCodeRequest;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.system.ApplicationHome;
import org.springframework.context.annotation.Profile;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Author: 中文输入法发挥不稳定的刘川
 * @Date: 2024/10/11 下午5:05
 */
@RestController
@RequestMapping("/ttest")
@Slf4j
public class TestController extends BaseController {

    @Resource
    UserInfoPOMapper userInfoPOMapper;
    @Resource
    BloomFilterUtil bloomFilterUtil;
    @Resource
    private LlmService llmService;
    @Resource
    private QrCodeApiService qrCodeApiService;
    @Resource
    private Cms2QrCodeService cms2QrCodeService;

    @GetMapping("/hello")
    public String hello() {
        return "world";
    }

    @Profile("local")
    @GetMapping("/hello2")
    public String hello2() {
        bloomFilterUtil.buildBloomFilter("testBLF", ()-> userInfoPOMapper.selectAll().stream().map(UserInfoPO::getId).toList(), 100000, 0.01, 10, TimeUnit.MINUTES);
        BloomFilterUtil.BloomFilterStats stats= bloomFilterUtil.getBloomFilterStats("testBLF");
        log.debug("bloomFilterStats:{}", stats);
        var result = bloomFilterUtil.containsElement("testBLF", 1);
        log.debug("result:{}", result);
        var result2 = bloomFilterUtil.containsElement("testBLF", 11232222);
        log.debug("result2:{}", result2);


        List cel=userInfoPOMapper.selectAll().stream().toList();
        bloomFilterUtil.buildBloomFilter("testBLF_CellPhone", ()-> userInfoPOMapper.selectAll().stream().map(UserInfoPO::getCellPhone).toList(), 100000, 0.001, 10, TimeUnit.MINUTES);
        var result3 = bloomFilterUtil.containsElement("testBLF_CellPhone", 13800138000L);
        log.debug("result3:{}", result3);
        var result4 = bloomFilterUtil.containsElement("testBLF_CellPhone", 11232222);
        log.debug("result4:{}", result4);

        return "world2";
    }

    @GetMapping("/hello3")
    public String hello3() {
        AigcApiResponse result = llmService.processImagePrompt(
                "图片中描述了什么?",
                "https://dev-vroom-1311485584.cos.ap-beijing.myqcloud.com/BA29F400467F43B4BF3F27235D5C4492.png"
        );
        return result.toString();
    }

    @GetMapping("/hello4")
    public String hello4() {
        List<UserInfoPO> result = userInfoPOMapper.bathSelectUser(List.of(191919191919L));
        return result.toString();
    }

    @GetMapping("/processImagePrompt")
    public String processImagePrompt(@RequestParam String prompt, @RequestParam String imageUrl) {
        AigcApiResponse result = llmService.processImagePrompt(
                prompt,
                imageUrl
        );
        return JsonUtil.toJsonString(result);
    }

//    @SneakyThrows
//    @GetMapping("/processImagePromptName")
//    public String processImagePromptName(@RequestParam String promptName, @RequestParam String imageUrl) {
//        // 校验 promptName 不为空
//        if (promptName == null || promptName.trim().isEmpty()) {
//            throw new IllegalArgumentException("promptName 不能为空");
//        }
//        String filePath = new ApplicationHome(getClass()).getSource().getParent() + "/" + promptName + ".md";
//        String fileContent = Files.readString(Paths.get(filePath));
//        AigcApiResponse result = llmService.processImagePrompt(
//                fileContent,
//                imageUrl
//        );
//        return JsonUtil.toJsonString(result);
//    }

    @GetMapping("/addCms2QrCode")
    public void addCms2QrCode(@RequestBody AddQrCodeRequest addQrCodeRequest) {
        QrCodeApiResponse<List<QrCodeInfo>> qrCodeApiResponse = qrCodeApiService.add(addQrCodeRequest);
        log.debug("添加二维码：{}-qrCodeApiResponse:{}","addCms2QrCode", JsonUtil.toJsonString(qrCodeApiResponse));
    }

    @GetMapping("/updateCms2QrCode")
    public void updateCms2QrCode(@RequestBody UpdateQrCodeRequest updateQrCodeRequest) {
        QrCodeApiResponse<QrCodeInfo> qrCodeApiResponse = qrCodeApiService.update(updateQrCodeRequest);
        log.debug("更新二维码：{}-qrCodeApiResponse:{}", "updateCms2QrCode",JsonUtil.toJsonString(qrCodeApiResponse));
    }

    @GetMapping("/searchCms2QrCode")
    public void searchCms2QrCode(@RequestParam String courseId, @RequestParam String randomCode) {
        QrCodeApiResponse<List<QrCodeInfo>> qrCodeApiResponse = qrCodeApiService.search(courseId, randomCode);
        log.debug("搜索二维码：{}-qrCodeApiResponse:{}", "searchCms2QrCode",JsonUtil.toJsonString(qrCodeApiResponse));
    }

    @GetMapping("/listCms2QrCode")
    public void listCms2QrCode(@RequestParam String courseId) {
        QrCodeApiResponse<List<QrCodeInfo>> qrCodeApiResponse = qrCodeApiService.list(courseId);
        log.debug("查询二维码列表：{}-qrCodeApiResponse:{}", "listCms2QrCode",JsonUtil.toJsonString(qrCodeApiResponse));
    }

    @GetMapping("/displayCms2QrCode")
    public ResponseEntity<org.springframework.core.io.Resource> displayCms2QrCode(@RequestParam("id") Long id) {
        ResponseEntity<org.springframework.core.io.Resource> display = qrCodeApiService.display(id);
        return qrCodeApiService.display(id);
    }

    @GetMapping("/downloadCms2QrCode")
    public ResponseEntity<org.springframework.core.io.Resource> downloadCms2QrCode(@RequestParam("id") Long id) {
        return qrCodeApiService.download(id);
    }

    @GetMapping("/importCms2QrCode")
    public void importCms2QrCode(@RequestParam("courseId") String courseId, @RequestPart("file") MultipartFile file) {
        QrCodeApiResponse<Boolean> qrCodeApiResponse = qrCodeApiService.importQrcode(cms2QrCodeService.genToken(getCurrentUserId()), courseId, file);
        log.debug("导入二维码：{}-qrCodeApiResponse:{}", "importCms2QrCode",JsonUtil.toJsonString(qrCodeApiResponse));
    }

    @GetMapping("/exportCms2QrCode")
    public ResponseEntity<org.springframework.core.io.Resource> exportCms2QrCode(@RequestParam("courseId") String courseId) {
        ResponseEntity<org.springframework.core.io.Resource> response = qrCodeApiService.exportQrcode(cms2QrCodeService.genToken(getCurrentUserId()), courseId);
        return qrCodeApiService.exportQrcode(cms2QrCodeService.genToken(getCurrentUserId()), courseId);
    }


    @GetMapping("/throwException")
    public Response<String> throwException(@RequestParam("exception") String exception) {
        throw new RuntimeException(exception);
    }
}
