package com.unipus.digitalbook.conf.tenant;

import com.unipus.digitalbook.common.utils.UrlUtil;
import com.unipus.digitalbook.model.po.tenant.TenantSubscribePO;
import com.unipus.digitalbook.service.TenantSubscribeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;
import reactor.netty.resources.ConnectionProvider;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
@Slf4j
public class TenantSubscribeConfig {

    private final ExchangeFilterFunction exchangeFilterFunction;

    public TenantSubscribeConfig(TenantSubscribeService tenantSubscribeService, @Qualifier("loggingFilter") ExchangeFilterFunction exchangeFilterFunction) {
        this.exchangeFilterFunction = exchangeFilterFunction;
        List<TenantSubscribePO> tenantSubscribePOList = tenantSubscribeService.selectAll();

        tenantSubscribePOList.stream()
                .map(TenantSubscribePO::getKafkaBootstrapServers)
                .filter(StringUtils::hasText)
                .distinct().forEach(this::kafkaTemplate);
        tenantSubscribePOList.stream()
                .map(TenantSubscribePO::getHttpUrl)
                .filter(StringUtils::hasText)
                .map(UrlUtil::getDomainFromUrl)
                .distinct().forEach(this::webClient);
    }

    // 缓存 KafkaTemplate 实例，避免重复创建
    private final Map<String, KafkaTemplate<String, String>> templateCache = new ConcurrentHashMap<>();

    // 缓存 webClient 实例，避免重复创建
    private final Map<String, WebClient> webClientCache = new ConcurrentHashMap<>();

    /**
     * 创建或获取 KafkaTemplate 实例
     *
     * @param bootstrapServers Kafka 集群地址
     * @return KafkaTemplate 实例
     */
    public KafkaTemplate<String, String> kafkaTemplate(String bootstrapServers) {
        // 检查缓存中是否已存在实例
        return templateCache.computeIfAbsent(bootstrapServers, this::createKafkaTemplate);
    }

    /**
     * 创建 KafkaTemplate 实例
     *
     * @param bootstrapServers Kafka 集群地址
     * @return KafkaTemplate 实例
     */
    private KafkaTemplate<String, String> createKafkaTemplate(String bootstrapServers) {
        Map<String, Object> configProps = new HashMap<>();
        configProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        configProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        configProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
//        configProps.put(ProducerConfig.ACKS_CONFIG, "all"); // 确保所有副本都确认
//        configProps.put(ProducerConfig.RETRIES_CONFIG, 3); // 使用幂等性配置来确保消息不重复发送
//        configProps.put(ProducerConfig.ENABLE_IDEMPOTENCE_CONFIG, true); // 启用幂等性，确保消息不会重复发送
//        configProps.put(ProducerConfig.LINGER_MS_CONFIG, 5); // 消息发送延迟，0表示立即发送
//        configProps.put(ProducerConfig.MAX_IN_FLIGHT_REQUESTS_PER_CONNECTION, 5); // 平衡吞吐量消息按顺序发送
//        configProps.put(ProducerConfig.REQUEST_TIMEOUT_MS_CONFIG, Duration.ofSeconds(5).toMillis()); // 生产者等待 Broker 响应的最大时间（毫秒）
//        configProps.put(ProducerConfig.MAX_BLOCK_MS_CONFIG, Duration.ofSeconds(10).toMillis());
//        configProps.put(ProducerConfig.DELIVERY_TIMEOUT_MS_CONFIG, Duration.ofSeconds(20).toMillis()); // 消息的总发送超时时间（包括重试时间）


        ProducerFactory<String, String> producerFactory = new DefaultKafkaProducerFactory<>(configProps);
        return new KafkaTemplate<>(producerFactory);
    }

    public void restartKafkaTemplate(String bootstrapServers) {
        KafkaTemplate<String, String> kafkaTemplate = templateCache.remove(bootstrapServers);
        if (kafkaTemplate != null) {
            kafkaTemplate.destroy();
        }
        kafkaTemplate(bootstrapServers); // 重新创建新的 KafkaTemplate 实例
        log.info("KafkaTemplate for {} restarted", bootstrapServers);
    }

    /**
     * 创建或获取 webClient 实例
     *
     * @param host baseUri
     * @return webClient 实例
     */
    public WebClient webClient(String host) {
        // 检查缓存中是否已存在实例
        return webClientCache.computeIfAbsent(host, this::createWebClient);
    }

    /**
     * 创建 webClient 实例
     *
     * @param host baseUri
     * @return webClient 实例
     */
    private WebClient createWebClient(String host) {

        ConnectionProvider connectionProvider = ConnectionProvider.builder("myConnectionProvider")
                .maxConnections(200) // 最大连接数（并发连接数）
                .pendingAcquireTimeout(Duration.ofSeconds(5)) // 等待连接的超时时间
                .maxIdleTime(java.time.Duration.ofSeconds(30)) // 最大空闲时间
                .maxLifeTime(java.time.Duration.ofMinutes(5)) // 最大生命周期
                .build();
        HttpClient httpClient = HttpClient.create(connectionProvider)
                .responseTimeout(Duration.ofSeconds(1)) // 响应超时时间
                .doOnConnected(conn -> conn
                        .addHandlerLast(new io.netty.handler.timeout.ReadTimeoutHandler(5)) // 读取超时
                        .addHandlerLast(new io.netty.handler.timeout.WriteTimeoutHandler(5)) // 写入超时
                );
        // 配置编码和解码
        ExchangeStrategies strategies = ExchangeStrategies.builder()
                .codecs(configurer -> {
                    configurer.defaultCodecs().maxInMemorySize(16 * 1024 * 1024); // 最大内存大小
                })
                .build();
        return WebClient.builder()
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .exchangeStrategies(strategies)
                .baseUrl(host)
                .filter(exchangeFilterFunction)
                .filter((request, next) -> next.exchange(request)
                        .retryWhen(Retry.fixedDelay(3, Duration.ofSeconds(1)))) // 全局重试策略
                .build();
    }

//    @Bean
//    public ConcurrentKafkaListenerContainerFactory<?, ?> manualCommitContainerFactory(
//            ConsumerFactory<Object, Object> consumerFactory) {
//        ConcurrentKafkaListenerContainerFactory<Object, Object> factory =
//                new ConcurrentKafkaListenerContainerFactory<>();
//        factory.setConsumerFactory(consumerFactory);
//        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL);
//        Properties properties = factory.getContainerProperties().getKafkaConsumerProperties();
//        properties.setProperty(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, "1");
//        log.info("manualCommitContainerFactory prop={}", JSON.toJSONString(properties));
//        return factory;
//    }

}
