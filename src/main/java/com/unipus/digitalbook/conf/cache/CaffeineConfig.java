package com.unipus.digitalbook.conf.cache;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.unipus.digitalbook.dao.ChapterVersionPOMapper;
import com.unipus.digitalbook.dao.TenantChannelPOMapper;
import com.unipus.digitalbook.dao.TenantSubscribePOMapper;
import com.unipus.digitalbook.model.entity.chapter.ChapterNode;
import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.QuestionId;
import com.unipus.digitalbook.model.entity.question.QuestionText;
import com.unipus.digitalbook.model.entity.tenant.TenantSubscribeID;
import com.unipus.digitalbook.model.po.chapter.ChapterNodePO;
import com.unipus.digitalbook.model.po.chapter.ChapterVersionPO;
import com.unipus.digitalbook.model.po.tenant.TenantChannelPO;
import com.unipus.digitalbook.model.po.tenant.TenantSubscribePO;
import com.unipus.digitalbook.service.QuestionService;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Configuration
public class CaffeineConfig {
    @Resource
    private ChapterVersionPOMapper chapterVersionPOMapper;

    @Resource
    private TenantSubscribePOMapper tenantSubscribePOMapper;

    @Resource
    private TenantChannelPOMapper tenantChannelPOMapper;

    @Resource
    private QuestionService  questionService;

    @Bean
    public LoadingCache<Long, Map<String, ChapterNode>> chapterNodeCache() {
        return Caffeine.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(Duration.ofDays(3))
                .build(this::getNodeMapByChapterVersionId);
    }

    @Bean
    public LoadingCache<QuestionId, BigQuestionGroup> questionForJudgeCache() {
        return Caffeine.newBuilder()
                .maximumSize(10_000)
                .expireAfterWrite(Duration.ofDays(3))
                .build(questionId -> {
                    BigQuestionGroup bigQuestion = questionService.getBigQuestion(questionId.getBizQuestionId(), questionId.getVersionNumber());
                    return processQuestionForJudge(bigQuestion);
                });
    }

    @Bean
    public LoadingCache<TenantSubscribeID, TenantSubscribePO> tenantSubscribeCache() {
        return Caffeine.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(Duration.ofHours(1))
                .build(tenantSubscribeID -> {
                    return tenantSubscribePOMapper.selectByTenantIdAndMessageTopic(tenantSubscribeID.tenantId(), tenantSubscribeID.messageTopic());
                });
    }

    @Bean
    public LoadingCache<TenantSubscribeID, Map<Integer, TenantChannelPO>> tenantChannelCache() {
        return Caffeine.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(Duration.ofHours(1))
                .build(tenantSubscribeID -> {
                    List<TenantChannelPO> tenantChannelPOS = tenantChannelPOMapper.selectByTenantIdAndMessageTopic(tenantSubscribeID.tenantId(), tenantSubscribeID.messageTopic());
                    if (tenantChannelPOS == null || tenantChannelPOS.isEmpty()) {
                        return Collections.emptyMap();
                    }
                    return tenantChannelPOS.stream().collect(Collectors.toMap(TenantChannelPO::getChannel, tenantChannelPO -> tenantChannelPO, (existing, replacement) -> existing));
                });
    }

    private BigQuestionGroup processQuestionForJudge(BigQuestionGroup bigQuestion) {
        bigQuestion.setSetting(null);
        List<Question> questions = bigQuestion.getQuestions();
        questions.forEach(this::questionPeek);
        return bigQuestion;
    }

    private void questionPeek(Question question) {
        question.setContent(null);
        questionTextPeek(question.getQuestionText());
        if (CollectionUtils.isEmpty(question.getQuestions())) {
            return;
        }
        question.getQuestions().forEach(this::questionPeek);
    }

    private void questionTextPeek(QuestionText questionText) {
        questionText.setText(null);
        questionText.setRelevancy(null);
        questionText.setMedia(null);
        questionText.setPhoneticSymbol(null);
        questionText.setAnswerWordLimit(null);
        questionText.setPrepareTime(null);
        questionText.setAnswerTime(null);
    }

    private Map<String, ChapterNode> getNodeMapByChapterVersionId(Long chapterVersionId) {
        ChapterVersionPO chapterVersionPO = chapterVersionPOMapper.selectChapterNodeByChapterVersionId(chapterVersionId);
        if (chapterVersionPO == null || CollectionUtils.isEmpty(chapterVersionPO.getTotalStruct())) {
            return Collections.emptyMap();
        }
        Map<String, ChapterNode> nodeMap = new LinkedHashMap<>();
        List<ChapterNodePO> totalStruct = chapterVersionPO.getTotalStruct();
        if (CollectionUtils.isEmpty(totalStruct)) {
            return Collections.emptyMap();
        }
        List<ChapterNode> entityList = totalStruct.stream()
                .map(ChapterNodePO::toEntity)
                .toList();
        ChapterNode.buildTree(entityList);
        for (int i = 0; i < entityList.size(); i++) {
            ChapterNode entity = entityList.get(i);
            entity.setOffset(i);
            String questionType = entity.getQuestionType();
            if (!StringUtils.hasText(questionType)) {
                entity.setText(null);
            }
            nodeMap.put(entity.getId(), entity);
        }
        return nodeMap;
    }

}
