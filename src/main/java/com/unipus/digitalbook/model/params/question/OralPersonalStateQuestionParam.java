package com.unipus.digitalbook.model.params.question;

import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.QuestionText;
import com.unipus.digitalbook.model.entity.question.type.OralPersonalStateQuestion;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * 个人陈述题参数
 */
public class OralPersonalStateQuestionParam extends QuestionBaseParam {
    @Schema(description = "准备时间")
    private Integer prepareTime;

    @Schema(description = "作答间隔时长")
    private Integer answerTime;

    @Override
    public void valid() {
        if (!CollectionUtils.isEmpty(getChildren())) {
            if (!StringUtils.hasText(getQuesText())) {
                throw new IllegalArgumentException("题干不能为空");
            }
        }
    }

    @Override
    protected Question toQuestion(QuestionText questionText) {
        OralPersonalStateQuestion question = new OralPersonalStateQuestion();
        QuestionText currentQuestionText = new QuestionText(getQuesText(), getQuesTextString());
        currentQuestionText.setPrepareTime(getPrepareTime());
        currentQuestionText.setAnswerTime(getAnswerTime());
        question.setQuestionText(currentQuestionText);
        return question;
    }

    public Integer getPrepareTime() {
        return prepareTime;
    }

    public void setPrepareTime(Integer prepareTime) {
        this.prepareTime = prepareTime;
    }

    public Integer getAnswerTime() {
        return answerTime;
    }

    public void setAnswerTime(Integer answerTime) {
        this.answerTime = answerTime;
    }
}
