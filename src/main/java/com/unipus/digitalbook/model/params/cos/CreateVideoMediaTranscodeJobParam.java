package com.unipus.digitalbook.model.params.cos;

import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.util.StringUtils;

/**
 * 创建视频转码任务参数
 *
 * <AUTHOR>
 * @date 2025/01/10
 */
@Data
@Schema(description = "创建视频转码任务参数")
public class CreateVideoMediaTranscodeJobParam implements Params {

    @Schema(description = "文件URL", requiredMode = Schema.RequiredMode.REQUIRED)
    private String url;

    @Schema(description = "文件hash值，用于检查是否已转码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hash;

    @Override
    public void valid() {
        if (!StringUtils.hasText(url)) {
            throw new IllegalArgumentException("文件URL不能为空");
        }
        if (!StringUtils.hasText(hash)) {
            throw new IllegalArgumentException("文件hash值不能为空");
        }
    }
}
