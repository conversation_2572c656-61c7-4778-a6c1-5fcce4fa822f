package com.unipus.digitalbook.model.params.question;

import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.QuestionText;
import com.unipus.digitalbook.model.entity.question.type.WritingQuestion;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * 写作题题
 */
public class WritingQuestionParam extends QuestionBaseParam {
    @Schema(description = "答案字数限制")
    private Integer answerWordLimit;

    @Override
    public void valid() {
        if (!CollectionUtils.isEmpty(getChildren())) {
            if (!StringUtils.hasText(getQuesText())) {
                throw new IllegalArgumentException("题干不能为空");
            }
        }
    }


    @Override
    protected Question toQuestion(QuestionText questionText) {
        QuestionText currentQuestionText = new QuestionText(questionText.getText(), questionText.getPlainText());
        currentQuestionText.setAnswerWordLimit(getAnswerWordLimit());
        WritingQuestion question = new WritingQuestion();
        question.setQuestionText(currentQuestionText);
        return question;
    }

    public Integer getAnswerWordLimit() {
        return answerWordLimit;
    }

    public void setAnswerWordLimit(Integer answerWordLimit) {
        this.answerWordLimit = answerWordLimit;
    }
}
