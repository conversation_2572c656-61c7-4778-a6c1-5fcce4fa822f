package com.unipus.digitalbook.model.params.question;

import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.QuestionText;
import com.unipus.digitalbook.model.entity.question.type.VocabularyQuestion;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * 单词本参数
 */
public class VocabularyQuestionParam extends QuestionBaseParam {

    @Schema(description = "准备时间")
    private Integer prepareTime;

    @Schema(description = "作答间隔时长")
    private Integer answerTime;

    @Schema(description = "媒体")
    private String media;

    @Schema(description = "音标")
    private String phoneticSymbol;
    @Override
    public void valid() {
        if (CollectionUtils.isEmpty(getChildren())) {
            if (CollectionUtils.isEmpty(getAnswers())) {
                throw new IllegalArgumentException("单词不能为空");
            }
        }
    }

    @Override
    protected Question toQuestion(QuestionText questionText) {
        VocabularyQuestion question = new VocabularyQuestion();
        QuestionText currentQuestionText = new QuestionText(getQuesText(), getQuesTextString());
        currentQuestionText.setMedia(getMedia());
        currentQuestionText.setPhoneticSymbol(getPhoneticSymbol());
        currentQuestionText.setPrepareTime(getPrepareTime());
        currentQuestionText.setAnswerTime(getAnswerTime());
        question.setQuestionText(currentQuestionText);
        return question;
    }

    public Integer getPrepareTime() {
        return prepareTime;
    }

    public void setPrepareTime(Integer prepareTime) {
        this.prepareTime = prepareTime;
    }

    public Integer getAnswerTime() {
        return answerTime;
    }

    public void setAnswerTime(Integer answerTime) {
        this.answerTime = answerTime;
    }

    public String getMedia() {
        return media;
    }

    public void setMedia(String media) {
        this.media = media;
    }

    public String getPhoneticSymbol() {
        return phoneticSymbol;
    }

    public void setPhoneticSymbol(String phoneticSymbol) {
        this.phoneticSymbol = phoneticSymbol;
    }
}
