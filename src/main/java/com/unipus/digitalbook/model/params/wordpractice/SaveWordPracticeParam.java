package com.unipus.digitalbook.model.params.wordpractice;

import com.unipus.digitalbook.model.entity.wordpractice.WordPractice;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "词汇学练请求参数")
public class SaveWordPracticeParam {

    /**
     * id
     */
    @Schema(description = "ID")
    private Long id;

    /**
     * 业务ID
     */
    @Schema(description = "业务ID")
    private String bizId;
    /**
     * 章节ID
     */
    @Schema(description = "章节ID")
    private String chapterId;
    /**
     * 内容数据json
     */
    @Schema(description = "内容数据json")
    private String jsonData;
    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;
    /**
     * 教材ID
     */
    @Schema(description = "教材ID")
    private String bookId;
    /**
     * 章节ID集合
     */
    @Schema(description = "章节ID集合")
    private List<String> chapterIds;
    /**
     * 日志id
     */
    @Schema(description = "日志id")
    private Long logId;

    public WordPractice toEntity() {
        WordPractice wordPractice = new WordPractice();
        wordPractice.setId(this.id);
        wordPractice.setBizId(this.bizId);
        wordPractice.setParentId(this.chapterId);
        wordPractice.setJsonData(this.jsonData);
        wordPractice.setName(this.name);
        wordPractice.setBookId(this.bookId);
        return wordPractice;
    }
}
