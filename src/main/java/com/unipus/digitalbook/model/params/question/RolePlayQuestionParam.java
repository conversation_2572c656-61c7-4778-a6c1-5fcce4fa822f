package com.unipus.digitalbook.model.params.question;

import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.QuestionText;
import com.unipus.digitalbook.model.entity.question.type.RolePlayQuestion;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.CollectionUtils;

/**
 * 角色扮演题参数
 */
public class RolePlayQuestionParam extends QuestionBaseParam {
    @Schema(description = "准备时间")
    private Integer prepareTime;

    @Schema(description = "作答间隔时长")
    private Integer answerTime;

    @Schema(description = "角色")
    private String role;

    @Schema(description = "媒体")
    private String media;
    @Override
    public void valid() {

    }

    @Override
    protected Question toQuestion(QuestionText questionText) {
        RolePlayQuestion rolePlayQuestion = new RolePlayQuestion();
        QuestionText currentQuestionText = new QuestionText(getQuesText(), getQuesTextString());
        currentQuestionText.setMedia(getMedia());
        currentQuestionText.setRole(getRole());
        if (!CollectionUtils.isEmpty(getRelevancyList())) {
            currentQuestionText.setRelevancy(getRelevancyList().stream().map(QuestionRelevancyParam::toEntity).toList());
        }
        currentQuestionText.setPrepareTime(getPrepareTime());
        currentQuestionText.setAnswerTime(getAnswerTime());
        rolePlayQuestion.setQuestionText(currentQuestionText);
        return rolePlayQuestion;
    }

    public Integer getPrepareTime() {
        return prepareTime;
    }

    public void setPrepareTime(Integer prepareTime) {
        this.prepareTime = prepareTime;
    }

    public Integer getAnswerTime() {
        return answerTime;
    }

    public void setAnswerTime(Integer answerTime) {
        this.answerTime = answerTime;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public String getMedia() {
        return media;
    }

    public void setMedia(String media) {
        this.media = media;
    }
}
