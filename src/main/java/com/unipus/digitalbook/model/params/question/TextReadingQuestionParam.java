package com.unipus.digitalbook.model.params.question;

import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.QuestionText;
import com.unipus.digitalbook.model.entity.question.type.TextReadingQuestion;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.CollectionUtils;

/**
 * 文本跟读题参数
 */
public class TextReadingQuestionParam extends QuestionBaseParam {
    @Schema(description = "准备时间")
    private Integer prepareTime;

    @Schema(description = "作答间隔时长")
    private Integer answerTime;

    @Schema(description = "媒体")
    private String media;
    @Override
    public void valid() {

    }

    @Override
    protected Question toQuestion(QuestionText questionText) {
        TextReadingQuestion textReadingQuestion = new TextReadingQuestion();
        QuestionText currentQuestionText = new QuestionText(getQuesText(), getQuesTextString());
        currentQuestionText.setMedia(getMedia());
        if (!CollectionUtils.isEmpty(getRelevancyList())) {
            currentQuestionText.setRelevancy(getRelevancyList().stream().map(QuestionRelevancyParam::toEntity).toList());
        }
        currentQuestionText.setPrepareTime(getPrepareTime());
        currentQuestionText.setAnswerTime(getAnswerTime());
        textReadingQuestion.setQuestionText(currentQuestionText);
        return textReadingQuestion;
    }

    public Integer getPrepareTime() {
        return prepareTime;
    }

    public void setPrepareTime(Integer prepareTime) {
        this.prepareTime = prepareTime;
    }

    public Integer getAnswerTime() {
        return answerTime;
    }

    public void setAnswerTime(Integer answerTime) {
        this.answerTime = answerTime;
    }

    public String getMedia() {
        return media;
    }

    public void setMedia(String media) {
        this.media = media;
    }
}
