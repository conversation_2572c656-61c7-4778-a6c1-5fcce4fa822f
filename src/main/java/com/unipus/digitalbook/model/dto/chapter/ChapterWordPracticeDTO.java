package com.unipus.digitalbook.model.dto.chapter;

import com.unipus.digitalbook.model.dto.wordpractice.WordPracticeListItemDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Schema(description = "教材章节")
public class ChapterWordPracticeDTO implements Serializable {

    @Schema(description = "章节ID")
    private String id;

    @Schema(description = "章节编号")
    private Integer chapterNumber;

    @Schema(description = "章节名称")
    private String name;

    @Schema(description = "词汇学练列表")
    private List<WordPracticeListItemDTO> wordPracticeList;
}
