package com.unipus.digitalbook.model.events;

import com.unipus.digitalbook.model.entity.action.ContentNode;
import com.unipus.digitalbook.model.entity.action.UserAction;
import com.unipus.digitalbook.model.enums.ContentTypeEnum;

public class UserActionEvent {
    private Long tenantId;
    private String openId;
    private Long contentVersionId;
    private String contentId;
    private ContentNode contentNode;
    private String ip;

    private String dataPackage;

    private ContentTypeEnum contentType;

    private String bookId;

    private String bookVersionNumber;

    private String envPartition;

    public UserActionEvent(){}

    public UserActionEvent(ContentNode node, UserAction userAction) {
        this.tenantId = userAction.getTenantId();
        this.openId = userAction.getOpenId();
        this.contentNode = node;
        this.contentId = userAction.getContentId();
        this.contentVersionId = userAction.getContentVersionId();
        this.ip = userAction.getIp();
        this.dataPackage = userAction.getDataPackage();
        this.contentType = userAction.getContentType();
        this.bookId = userAction.getBookId();
        this.bookVersionNumber = userAction.getBookVersionNumber();
        this.envPartition = userAction.getEnvPartition();
    }

    public UserAction toUserAction() {
        UserAction userAction = new UserAction();
        userAction.setTenantId(tenantId);
        userAction.setOpenId(openId);
        userAction.setContentId(contentId);
        userAction.setContentVersionId(contentVersionId);
        userAction.setIp(ip);
        userAction.setDataPackage(dataPackage);
        userAction.setContentType(contentType);
        userAction.setBookId(bookId);
        userAction.setBookVersionNumber(bookVersionNumber);
        userAction.setEnvPartition(envPartition);
        return userAction;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getDataPackage() {
        return dataPackage;
    }

    public void setDataPackage(String dataPackage) {
        this.dataPackage = dataPackage;
    }

    public Long getContentVersionId() {
        return contentVersionId;
    }

    public void setContentVersionId(Long contentVersionId) {
        this.contentVersionId = contentVersionId;
    }

    public String getContentId() {
        return contentId;
    }

    public void setContentId(String contentId) {
        this.contentId = contentId;
    }

    public ContentNode getContentNode() {
        return contentNode;
    }

    public void setContentNode(ContentNode contentNode) {
        this.contentNode = contentNode;
    }

    public ContentTypeEnum getContentType() {
        return contentType;
    }

    public void setContentType(ContentTypeEnum contentType) {
        this.contentType = contentType;
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getBookVersionNumber() {
        return bookVersionNumber;
    }

    public void setBookVersionNumber(String bookVersionNumber) {
        this.bookVersionNumber = bookVersionNumber;
    }

    public String getEnvPartition() {
        return envPartition;
    }

    public void setEnvPartition(String envPartition) {
        this.envPartition = envPartition;
    }
}
