package com.unipus.digitalbook.model.events;

public class UseBookEvent {
    /**
     * 教材id
     */
    private String bookId;

    /**
     * 教材版本
     */
    private String bookVersion;

    /**
     * 租户id
     */
    private Long tenantId;

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getBookVersion() {
        return bookVersion;
    }

    public void setBookVersion(String bookVersion) {
        this.bookVersion = bookVersion;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }
}
