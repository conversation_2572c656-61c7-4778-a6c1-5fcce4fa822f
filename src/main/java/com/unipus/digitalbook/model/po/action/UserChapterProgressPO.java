package com.unipus.digitalbook.model.po.action;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户章节进度表
 * @TableName user_chapter_progress
 */
public class UserChapterProgressPO implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 用户id
     */
    private String openId;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 章节版本id
     */
    private Long chapterVersionId;

    /**
     * 章节id
     */
    private String chapterId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 进度
     */
    private byte[] progressBit;


    private String envPartition;

    /**
     * 主键
     */
    public Long getId() {
        return id;
    }

    /**
     * 主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 用户id
     */
    public String getOpenId() {
        return openId;
    }

    /**
     * 用户id
     */
    public void setOpenId(String openId) {
        this.openId = openId;
    }

    /**
     * 租户id
     */
    public Long getTenantId() {
        return tenantId;
    }

    /**
     * 租户id
     */
    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    /**
     * 章节版本id
     */
    public Long getChapterVersionId() {
        return chapterVersionId;
    }

    /**
     * 章节版本id
     */
    public void setChapterVersionId(Long chapterVersionId) {
        this.chapterVersionId = chapterVersionId;
    }

    /**
     * 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 进度
     */
    public byte[] getProgressBit() {
        return progressBit;
    }

    /**
     * 进度
     */
    public void setProgressBit(byte[] progressBit) {
        this.progressBit = progressBit;
    }

    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }

    public String getEnvPartition() {
        return envPartition;
    }

    public void setEnvPartition(String envPartition) {
        this.envPartition = envPartition;
    }
}