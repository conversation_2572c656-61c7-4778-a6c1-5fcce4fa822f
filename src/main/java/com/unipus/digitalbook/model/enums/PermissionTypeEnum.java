package com.unipus.digitalbook.model.enums;

/**
 * 数据权限类型
 */
public enum PermissionTypeEnum {

    NONE(0x00000000, "无"),
    SHARE(0x00000001, "共享"),
    READ(0x00000002, "阅读"),
    EDIT(0x00000004, "编辑"),
    OWNER(0x00000008, "拥有");

    private final Integer code;
    private final String desc;

    PermissionTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public Boolean match(PermissionTypeEnum permissionTypeEnum) {
        if(permissionTypeEnum==null){
            return Boolean.FALSE;
        }
        return this.match(permissionTypeEnum.getCode());
    }

    /**
     * 匹配权限
     * @param code 权限码(多种权限按位聚合)（0：直接匹配0，非0：按位匹配）
     * @return 是否匹配 true:匹配 false:不匹配
     */
    public Boolean match(Integer code) {
        if(code==null){
            return Boolean.FALSE;
        }
        if(code.equals(0)){
            return this.code.equals(0);
        }
        return this.code.equals(this.code & code);
    }

    /**
     * 匹配权限（严格匹配）
     * 要求input中每一位都做匹配判断，任意一位不满足则返回false
     * @param base 基础权限值(包含若干权限)
     * @param input 待匹配权限码(多种权限按位聚合)（0：直接匹配0，非0：按位匹配）
     * @return 匹配 true/不匹配 false:
     */
    public static Boolean strictMatch(Integer base, Integer input) {
        if(base==null && input==null){
            // 无需权限校验
            return Boolean.TRUE;
        }else
        if(base==null || input==null){
            // 无输入权限
            return Boolean.FALSE;
        }else {
            // 严格匹配
            return input.equals(base & input);
        }
    }

    /**
     * 匹配权限（任意匹配）
     * 要求input中任意一位匹配则返回true
     * @param base 基础权限值(包含若干权限)
     * @param input 待匹配权限码(多种权限按位聚合)（0：直接匹配0，非0：按位匹配）
     * @return 匹配 true/不匹配 false:
     */
    public static Boolean anyMatch(Integer base, Integer input) {
        if(base==null && input==null){
            // 无需权限校验
            return Boolean.TRUE;
        }else
        if(base==null || input==null){
            // 无输入权限
            return Boolean.FALSE;
        }else {
            return (base & input) != 0;
        }
    }

    /**
     * 计算权限逻辑
     * @param baseCode 基础权限值
     * @param newCode 变更权限码(添加权限：正常权限值>0，删除权限：权限值按位取反<0)
     * @return 计算后权限值
     */
    public static Integer compute(Integer baseCode, Integer newCode) {
        if(newCode==null){
            return baseCode;
        }
        if(newCode>0){
            // 添加个别权限
            return baseCode | newCode;
        }else
        if(newCode<0){
            // 删除个别权限
            return baseCode & newCode;
        }else{
            // 删除所有权限
            return 0;
        }
    }

    public static PermissionTypeEnum getPermissionTypeEnum(Integer code) {
        for (PermissionTypeEnum permissionTypeEnum : PermissionTypeEnum.values()) {
            if (permissionTypeEnum.getCode().equals(code)) {
                return permissionTypeEnum;
            }
        }
        return null;
    }

}
