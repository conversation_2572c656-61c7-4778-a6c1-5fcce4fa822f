<configuration>
    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%thread] %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 飞书推送 -->
    <appender name="FEISHU" class="com.unipus.digitalbook.aop.log.FeishuLogAppender">
        <webhookUrl>${FEISHU_WEBHOOK}</webhookUrl>
    </appender>
    <appender name="FEISHU_ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="FEISHU"/>
    </appender>

    <!-- 同时挂两个 appender -->
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FEISHU_ASYNC"/>
    </root>
</configuration>