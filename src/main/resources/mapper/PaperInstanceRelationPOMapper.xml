<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.PaperInstanceRelationPOMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.paper.PaperInstanceRelationPO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="paper_id" property="paperId" jdbcType="CHAR"/>
        <result column="paper_version_number" property="paperVersionNumber" jdbcType="CHAR"/>
        <result column="base_instance_id" property="baseInstanceId" jdbcType="CHAR"/>
        <result column="target_instance_id" property="targetInstanceId" jdbcType="CHAR"/>
        <result column="type" property="type" jdbcType="TINYINT"/>
        <result column="tenant_id" property="tenantId" jdbcType="BIGINT"/>
        <result column="open_id" property="openId" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="enable" property="enable" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, paper_id, paper_version_number,base_instance_id, target_instance_id, type, tenant_id, open_id, create_time, update_time, enable
    </sql>

    <!-- 插入或更新 -->
    <insert id="insertOrUpdate" parameterType="PaperInstanceRelationPO">
        INSERT INTO paper_instance_relation(paper_id, paper_version_number, base_instance_id, target_instance_id, type, tenant_id, open_id)
        VALUES (#{paperId}, #{paperVersionNumber}, #{baseInstanceId}, #{targetInstanceId}, #{type}, #{tenantId}, #{openId})
        ON DUPLICATE KEY UPDATE
        target_instance_id = Values(target_instance_id),
        enable = 1
    </insert>

    <!-- 查询 -->
    <select id="selectLatestInstanceRelation" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM paper_instance_relation
        WHERE enable = 1
            AND tenant_id = #{tenantId}
            AND open_id = #{openId}
            AND paper_id = #{paperId}
            <if test="paperVersionNumber != null">
                AND paper_version_number = #{paperVersionNumber}
            </if>
            AND type = #{type}
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <!-- 获取试卷关系 -->
    <select id="getRelationByInstanceId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
            paper_instance_relation
        WHERE
            (base_instance_id = #{instanceId} OR target_instance_id = #{instanceId})
            AND tenant_id = #{tenantId}
            AND open_id = #{openId}
            AND type = #{type}
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <!-- 更新目标实例ID -->
    <update id="updateTargetInstanceId" parameterType="PaperInstanceRelationPO">
        UPDATE paper_instance_relation
        <set>
            <if test="targetInstanceId != null">
                target_instance_id = #{targetInstanceId},
            </if>
        </set>
        WHERE enable = 1
        <!-- 根据业务逻辑调整更新条件，例如使用基础实例ID和目标实例ID组合 -->
        <if test="tenantId != null">
            AND tenant_id = #{tenantId}
        </if>
        <if test="openId != null">
            AND open_id = #{openId}
        </if>
        <if test="baseInstanceId != null">
            AND base_instance_id = #{baseInstanceId}
        </if>
        <if test="type != null">
            AND type = #{type}
        </if>
    </update>

    <!-- 删除预览卷作答记录 -->
    <update id="deletePreviewInfo">
        UPDATE paper_instance_relation
        SET enable = 0
        WHERE paper_id = #{paperId}
          AND paper_version_number = #{versionNumber}
          AND open_id = #{openId}
          AND tenant_id = #{tenantId}
    </update>
</mapper>