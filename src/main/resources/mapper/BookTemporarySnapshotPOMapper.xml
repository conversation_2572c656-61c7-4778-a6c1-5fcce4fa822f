<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.BookTemporarySnapshotPOMapper">
  <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.book.BookTemporarySnapshotPO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="book_id" jdbcType="CHAR" property="bookId" />
    <result column="chapter_id" jdbcType="CHAR" property="chapterId" />
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="enable" jdbcType="BIT" property="enable" />
  </resultMap>
  <sql id="Base_Column_List">
    id, book_id, chapter_id, content, create_time, update_time, create_by, update_by, `enable`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from book_temporary_snapshot
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.book.BookTemporarySnapshotPO" useGeneratedKeys="true">
    insert into book_temporary_snapshot
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bookId != null and bookId != ''">
        book_id,
      </if>
      <if test="chapterId != null and chapterId != ''">
        chapter_id,
      </if>
      <if test="content != null and content != ''">
        content,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bookId != null and bookId != ''">
        #{bookId,jdbcType=CHAR},
      </if>
      <if test="chapterId != null and chapterId != ''">
        #{chapterId,jdbcType=CHAR},
      </if>
      <if test="content != null and content != ''">
        #{content,jdbcType=LONGVARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.book.BookTemporarySnapshotPO">
    update book_temporary_snapshot
    <set>
      <if test="bookId != null and bookId != ''">
        book_id = #{bookId,jdbcType=CHAR},
      </if>
      <if test="chapterId != null and chapterId != ''">
        chapter_id = #{chapterId,jdbcType=CHAR},
      </if>
      <if test="content != null and content != ''">
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <!-- 根据教材ID和章节ID查询最近一次快照 -->
  <select id="selectLatestByBookIdAndChapterId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from book_temporary_snapshot
    where book_id = #{bookId,jdbcType=CHAR}
      and chapter_id = #{chapterId,jdbcType=CHAR}
      and enable = true
    order by id desc limit 1
  </select>
</mapper>