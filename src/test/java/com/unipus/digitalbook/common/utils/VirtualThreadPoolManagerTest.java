package com.unipus.digitalbook.common.utils;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

/**
 * 虚拟线程池管理器测试类
 */
@SpringBootTest
@Slf4j
public class VirtualThreadPoolManagerTest {

    @Resource
    private VirtualThreadPoolManager virtualThreadPoolManager;

    @Test
    public void testVirtualThreadExecution() throws InterruptedException {
        int taskCount = 10;
        CountDownLatch latch = new CountDownLatch(taskCount);

        log.info("开始提交{}个虚拟线程任务", taskCount);

        // 提交多个异步任务
        for (int i = 0; i < taskCount; i++) {
            final int taskId = i;
            virtualThreadPoolManager.executeAsync(() -> {
                try {
                    log.info("虚拟线程任务{}开始执行", taskId);
                    // 模拟耗时操作
                    Thread.sleep(2000);
                    log.info("虚拟线程任务{}执行完成", taskId);
                } catch (InterruptedException e) {
                    log.warn("虚拟线程任务{}被中断", taskId);
                    Thread.currentThread().interrupt();
                } finally {
                    latch.countDown();
                }
            });
        }

        // 等待所有任务完成
        boolean completed = latch.await(10, TimeUnit.SECONDS);
        log.info("所有任务是否完成: {}", completed);
    }

    @Test
    public void testCompletableFutureWithVirtualThread() {
        log.info("测试CompletableFuture与虚拟线程池的结合使用");

        CompletableFuture<String> future1 = virtualThreadPoolManager.supplyAsync(() -> {
            try {
                Thread.sleep(1000);
                return "任务1完成";
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return "任务1被中断";
            }
        });

        CompletableFuture<String> future2 = virtualThreadPoolManager.supplyAsync(() -> {
            try {
                Thread.sleep(1500);
                return "任务2完成";
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return "任务2被中断";
            }
        });

        // 等待所有任务完成
        CompletableFuture<Void> allTasks = CompletableFuture.allOf(future1, future2);
        
        try {
            allTasks.get(5, TimeUnit.SECONDS);
            log.info("任务1结果: {}", future1.get());
            log.info("任务2结果: {}", future2.get());
        } catch (Exception e) {
            log.error("任务执行异常", e);
        }
    }

    @Test
    public void testBatchProcessing() throws InterruptedException {
        log.info("测试批处理虚拟线程池");

        int batchSize = 5;
        CountDownLatch latch = new CountDownLatch(batchSize);

        for (int i = 0; i < batchSize; i++) {
            final int batchId = i;
            virtualThreadPoolManager.executeBatchAsync(() -> {
                try {
                    log.info("批处理任务{}开始执行", batchId);
                    Thread.sleep(1000);
                    log.info("批处理任务{}执行完成", batchId);
                } catch (InterruptedException e) {
                    log.warn("批处理任务{}被中断", batchId);
                    Thread.currentThread().interrupt();
                } finally {
                    latch.countDown();
                }
            });
        }

        boolean completed = latch.await(5, TimeUnit.SECONDS);
        log.info("所有批处理任务是否完成: {}", completed);
    }
}
