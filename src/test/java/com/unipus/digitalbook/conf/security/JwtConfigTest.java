package com.unipus.digitalbook.conf.security;

import com.unipus.digitalbook.model.constants.WebConstant;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.oauth2.jwt.*;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

import static com.unipus.digitalbook.model.constants.WebConstant.JWT_SERVICE_TICKET;
import static com.unipus.digitalbook.model.constants.WebConstant.JWT_USER_ID;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class JwtConfigTest {

    @Mock
    private JwtProperties jwtProperties;

    @InjectMocks
    private JwtConfig jwtConfig;

    private static final String SECRET = "5LiN5piv5ZCn5LiN5piv5ZCn77yM6L+Z5LmI54K55bel6LWE6L+Y5oOz5bmy5ZWl5ZGi";
    private static final String SUBJECT = "testUser";
    private static final String ISSUER = "https://ipublish.unipus.cn";
    private static final String CUSTOM_CLAIM_KEY = "role";
    private static final String CUSTOM_CLAIM_VALUE = "ADMIN";

    @BeforeEach
    void setUp() {
        when(jwtProperties.getSecret()).thenReturn(SECRET);
    }

    @Test
    void whenEncodingAndDecodingJWT_thenAllClaimsArePreserved() {
        // 初始化编码器和解码器
        JwtEncoder encoder = jwtConfig.jwtEncoder();
        JwtDecoder decoder = jwtConfig.jwtDecoder();

        // 准备测试数据
        Instant now = Instant.now();
        Instant expiresAt = now.plusSeconds(3600);
        Map<String, Object> claims = new HashMap<>();
        claims.put(CUSTOM_CLAIM_KEY, CUSTOM_CLAIM_VALUE);
        claims.put(WebConstant.JWT_USER_ID,1);


        JwtClaimsSet claimsSet = JwtClaimsSet.builder()
                .issuer("self")
                .issuedAt(now)
                .expiresAt(expiresAt)
                .subject(SUBJECT)
                .claims(claimsMap -> claimsMap.putAll(claims))
                .build();
        // 创建JWT
        Jwt jwt = encoder.encode(JwtEncoderParameters.from(claimsSet));


        System.out.println(jwt.getTokenValue());
        // 解码JWT
        Jwt decodedJwt = decoder.decode(jwt.getTokenValue());
        // 验证结果
        assertNotNull(decodedJwt);
        assertEquals(SUBJECT, decodedJwt.getSubject());
//        assertEquals(ISSUER, decodedJwt.getIssuer().toString());
        assertEquals(CUSTOM_CLAIM_VALUE, decodedJwt.getClaim(CUSTOM_CLAIM_KEY));
        assertNotNull(decodedJwt.getIssuedAt());
        assertNotNull(decodedJwt.getExpiresAt());
        assertEquals(now.getEpochSecond(), decodedJwt.getIssuedAt().getEpochSecond());
        assertEquals(expiresAt.getEpochSecond(), decodedJwt.getExpiresAt().getEpochSecond());
    }

    @Test
    void whenJwtHasExpired_thenThrowException() {
        // 初始化编码器和解码器
        JwtEncoder encoder = jwtConfig.jwtEncoder();
        JwtDecoder decoder = jwtConfig.jwtDecoder();

        // 创建一个已过期的JWT
        Instant now = Instant.now();
        Instant expired = now.minusSeconds(3600); // 1小时前过期
        Instant issAt = now.minusSeconds(3800); // 1小时前过期


        Jwt jwt = encoder.encode(JwtEncoderParameters.from(JwsHeader.with(() -> "HS256").build(),
                JwtClaimsSet.builder()
                        .subject(SUBJECT)
                        .issuer(ISSUER)  // 添加issuer
                        .issuedAt(issAt)
                        .expiresAt(expired)
                        .build()));

        // 验证解码时抛出异常
        assertThrows(JwtValidationException.class, () -> decoder.decode(jwt.getTokenValue()));
    }

    @Test
    void whenInvalidSignature_thenThrowException() {
        // 初始化编码器和解码器
        when(jwtProperties.getSecret()).thenReturn("1DifferentSecretKeyHereThatIsAlsoAtLeast256BitsLong");
        JwtEncoder encoder = jwtConfig.jwtEncoder();
        Instant now = Instant.now();
        Instant expired = now.plusSeconds(3600);

        // 创建JWT
        Jwt jwt = encoder.encode(JwtEncoderParameters.from(JwsHeader.with(() -> "HS256").build(),
                JwtClaimsSet.builder()
                        .subject(SUBJECT)
                        .issuedAt(Instant.now())
                        .expiresAt(expired)
                        .issuer(ISSUER)  // 添加issuer
                        .build()));

        // 使用不同的密钥创建另一个解码器
        when(jwtProperties.getSecret()).thenReturn("2DifferentSecretKeyHereThatIsAlsoAtLeast256BitsLong");
        JwtDecoder decoderWithDifferentKey = jwtConfig.jwtDecoder();
        // 验证使用不同密钥解码时抛出异常
        assertThrows(BadJwtException.class, () -> decoderWithDifferentKey.decode(jwt.getTokenValue()));
    }

    @Test
    void whenEncodingWithInstantClaims_thenCorrectlyHandled() {
        // 初始化编码器和解码器
        JwtEncoder encoder = jwtConfig.jwtEncoder();
        JwtDecoder decoder = jwtConfig.jwtDecoder();

        // 准备测试数据
        Instant customTimestamp = Instant.now();
        Map<String, Object> claims = new HashMap<>();
        claims.put("timestamp", customTimestamp);

        // 创建JWT
        Jwt jwt = encoder.encode(JwtEncoderParameters.from(JwsHeader.with(() -> "HS256").build(),
                JwtClaimsSet.builder()
                        .issuedAt(Instant.now())
                        .expiresAt(Instant.now().plusSeconds(1))
                        .issuer(ISSUER)  // 添加issuer
                        .claims(existingClaims -> existingClaims.putAll(claims))
                        .build()));

        // 解码JWT
        Jwt decodedJwt = decoder.decode(jwt.getTokenValue());

        // 验证结果
        assertNotNull(decodedJwt.getClaim("timestamp"));
        System.out.println(decodedJwt.getClaim("timestamp").toString());
        Long timestampValue = (Long) decodedJwt.getClaim("timestamp");
        Instant claimInstant = Instant.ofEpochSecond(timestampValue);
        assertEquals(customTimestamp.getEpochSecond(), claimInstant.getEpochSecond());
    }

    @Test
    void getTokenForDevAndTest() {
        // 初始化编码器和解码器
        JwtEncoder encoder = jwtConfig.jwtEncoder();
        JwtDecoder decoder = jwtConfig.jwtDecoder();

        // 准备测试数据
        Instant now = Instant.now();
        Instant expiresAt = now.plusSeconds(3600 * 24 * 1800);
        Map<String, Object> claims = new HashMap<>();
        claims.put(CUSTOM_CLAIM_KEY, CUSTOM_CLAIM_VALUE);
        claims.put(JWT_SERVICE_TICKET, "ST-1954-cm0IoNfpBVxVcYx1PVE4-sso-api-1");
        claims.put(JWT_USER_ID, 1045L);

        JwtClaimsSet claimsSet = JwtClaimsSet.builder()
                .issuer("self")
                .issuedAt(now)
                .expiresAt(expiresAt)
                .subject(SUBJECT)
                .claims(claimsMap -> claimsMap.putAll(claims))
                .build();
        // 创建JWT

        Jwt jwt = encoder.encode(JwtEncoderParameters.from(claimsSet));
        System.out.println("生成一个1小时的");
        System.out.println(jwt.getTokenValue());



    }


}