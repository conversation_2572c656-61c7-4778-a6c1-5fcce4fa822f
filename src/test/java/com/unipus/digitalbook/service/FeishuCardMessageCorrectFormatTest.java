package com.unipus.digitalbook.service;

import com.alibaba.fastjson.JSON;
import com.unipus.digitalbook.model.dto.feishu.FeishuCardMessage;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * 测试飞书卡片消息的正确格式
 */
public class FeishuCardMessageCorrectFormatTest {
    
    @Test
    public void testCorrectCardMessageFormat() {
        FeishuCardMessage cardMessage = buildCorrectCardMessage();
        
        // 转换为JSON并打印
        String json = JSON.toJSONString(cardMessage);
        System.out.println("生成的卡片消息JSON:");
        System.out.println(json);
        
        // 验证JSON格式是否正确
        assert json.contains("\"schema\":\"2.0\"");
        assert json.contains("\"header\"");
        assert json.contains("\"body\"");
        assert json.contains("\"elements\"");
    }
    
    private FeishuCardMessage buildCorrectCardMessage() {
        FeishuCardMessage cardMessage = new FeishuCardMessage();
        
        // 设置卡片头部
        FeishuCardMessage.CardHeader header = new FeishuCardMessage.CardHeader();
        FeishuCardMessage.CardTitle cardTitle = new FeishuCardMessage.CardTitle();
        cardTitle.setContent("测试卡片消息");
        cardTitle.setTag("plain_text");
        header.setTitle(cardTitle);
        cardMessage.setHeader(header);
        
        // 设置卡片主体
        FeishuCardMessage.CardBody body = new FeishuCardMessage.CardBody();
        List<FeishuCardMessage.CardElement> elements = new ArrayList<>();
        
        // 添加文本元素
        FeishuCardMessage.CardElement textElement = new FeishuCardMessage.CardElement();
        textElement.setTag("markdown");
        textElement.setContent("这是一条测试消息");
        textElement.setTextAlign("left");
        textElement.setTextSize("normal_v2");
        textElement.setMargin("0px 0px 0px 0px");
        elements.add(textElement);
        
        body.setElements(elements);
        cardMessage.setBody(body);
        
        return cardMessage;
    }
} 