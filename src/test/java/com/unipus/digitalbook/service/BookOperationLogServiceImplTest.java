package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.entity.template.PaperScoreTemplate;
import com.unipus.digitalbook.model.enums.BookOperationEnum;
import com.unipus.digitalbook.model.enums.PaperScoreTemplateTypeEnum;
import com.unipus.digitalbook.service.impl.BookOperationLogServiceImpl;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.UUID;

@Slf4j
@SpringBootTest
class BookOperationLogServiceImplTest {

    @Resource
    private BookOperationLogServiceImpl bookOperationLogService;

    @Resource
    private PaperScoreTemplateRelationService paperScoreTemplateRelationService;

    @Test
    void addOperationLog_ValidInput_MessageSentToKafka() {
        // Arrange
        String bookId = UUID.randomUUID().toString();
        Long operationUserId = 1L;
        // Act
        bookOperationLogService.log(bookId, operationUserId, "章节 1", BookOperationEnum.INSERT.getCode());

    }

    @Test
    public void testGetDetail() {
        PaperScoreTemplate paperScoreTemplate = paperScoreTemplateRelationService.getPaperScoreTemplate("MaFl3KZpTler8_cWVFRVow", PaperScoreTemplateTypeEnum.CHALLENGE);
        log.info("paperScoreTemplate:{}", paperScoreTemplate);
    }
}
