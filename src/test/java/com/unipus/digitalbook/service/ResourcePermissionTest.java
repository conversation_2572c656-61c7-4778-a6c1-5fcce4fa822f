package com.unipus.digitalbook.service;

import com.unipus.digitalbook.dao.ResourcePermissionPOMapper;
import com.unipus.digitalbook.model.common.PageParams;
import com.unipus.digitalbook.model.entity.UserInfo;
import com.unipus.digitalbook.model.entity.permission.ResourcePermission;
import com.unipus.digitalbook.model.entity.permission.ResourceUser;
import com.unipus.digitalbook.model.enums.PermissionTypeEnum;
import com.unipus.digitalbook.model.enums.ResourceTypeEnum;
import com.unipus.digitalbook.model.params.book.PermissionSearchParam;
import com.unipus.digitalbook.model.params.book.ResourceUserSearchParam;
import com.unipus.digitalbook.model.po.book.ResourcePermissionPO;
import com.unipus.digitalbook.publisher.standalone.ResourcePermitEventPublisher;
import com.unipus.digitalbook.service.impl.ResourcePermissionServiceImpl;
import com.unipus.digitalbook.service.impl.UserServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.UUID;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.doReturn;

@ExtendWith(MockitoExtension.class)
//@ExtendWith(SpringExtension.class)
@Slf4j
public class ResourcePermissionTest {

    @InjectMocks
    private ResourcePermissionServiceImpl resourcePermissionServiceImpl;
    @Mock
    private UserServiceImpl userServiceImpl;
    @Mock
    private ResourcePermissionPOMapper resourcePermissionPOMapper;
    @Mock
    private ResourcePermitEventPublisher resourcePermitEventPublisher;
    @Mock(strictness = Mock.Strictness.LENIENT)
    private StringRedisTemplate stringRedisTemplate;

    private List<ResourcePermission> baseEntities;
    private List<ResourcePermission> newEntities;
    private List<ResourcePermissionPO> basePoList;
    private List<ResourcePermissionPO> newPoList;
    private List<PermissionSearchParam> params;

    private List<ResourcePermission> buildEntities(Integer count, ResourceTypeEnum resourceType, PermissionTypeEnum permissionType){
        Random random = new Random();
        List<ResourcePermission> entities =  new ArrayList<>();
        for(int i = 0; i < count; i++){
            ResourcePermission ent = new ResourcePermission();
            ent.setResourceId("RID_TEST_" + UUID.randomUUID());
            ent.setResourceType(resourceType.getCode());
            ent.setUserId(random.nextLong());
            ent.setPermissionType(permissionType.getCode());
            entities.add(ent);
        }
        return entities;
    }

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        baseEntities = buildEntities(10, ResourceTypeEnum.BOOK, PermissionTypeEnum.SHARE);
        basePoList = baseEntities.stream().map(e->ResourcePermissionPO.fromEntity(e, 1L)).toList();
        params = baseEntities.stream().map(e->
                new PermissionSearchParam(e.getUserId(), ResourceTypeEnum.BOOK, PermissionTypeEnum.SHARE)).toList();
        // 模拟权限
        newEntities = baseEntities.stream().map(e->
                new ResourcePermission(e.getResourceId(),e.getResourceType(), e.getUserId(), e.getPermissionType(), null)).toList();
        newEntities.forEach(e->e.setPermissionType(e.getPermissionType() | PermissionTypeEnum.READ.getCode()));
        newEntities.getFirst().setPermissionType(newEntities.getFirst().getPermissionType() | PermissionTypeEnum.OWNER.getCode());
        newPoList = newEntities.stream().map(e->ResourcePermissionPO.fromEntity(e, 1L)).toList();
    }

    // 根据资源ID或用户ID获取权限信息列表
    @Test
    void getPermissions() {
        doReturn(basePoList).when(resourcePermissionPOMapper).selectPermissions(anyList());
        List<ResourcePermission> permissions = resourcePermissionServiceImpl.getPermissions(params);
        // 使用 AssertJ 进行属性比较, 递归比较对象的所有属性
        assertThat(permissions).usingRecursiveComparison().isEqualTo(baseEntities);
        // 使用 JUnit 进行属性比较, 只比较对象的指定属性
        assertEquals(permissions, baseEntities);
    }

    // 更新数据权限：正常（存在历史数据,与新数据一致）
    @Test
    void updatePermission_01() {
        doReturn(basePoList).when(resourcePermissionPOMapper).selectPermissions(anyList());
        doReturn(true).when(stringRedisTemplate).delete("anyKey");
        //doReturn(basePoList.size()).when(resourcePermissionPOMapper).batchInsertOrUpdatePermissions(basePoList);
        Boolean result = resourcePermissionServiceImpl.updateUserPermission(baseEntities, 1L);
        assertEquals(true, result);
    }

    // 更新数据权限：正常（存在历史数据,与新数据不同）
    @Test
    void updatePermission_02() {
        doReturn(basePoList).when(resourcePermissionPOMapper).selectPermissions(anyList());
        doReturn(true).when(stringRedisTemplate).delete("anyKey");
        doReturn(basePoList.size()).when(resourcePermissionPOMapper).batchInsertOrUpdatePermissions(anyList());
        Boolean result = resourcePermissionServiceImpl.updateUserPermission(newEntities, 1L);
        assertEquals(true, result);
    }

    // 更新数据权限：正常（无历史数据）
    @Test
    void updatePermission_03() {
        doReturn(List.of()).when(resourcePermissionPOMapper).selectPermissions(anyList());
        doReturn(true).when(stringRedisTemplate).delete("anyKey");
        doReturn(basePoList.size()).when(resourcePermissionPOMapper).batchInsertOrUpdatePermissions(anyList());
        Boolean result = resourcePermissionServiceImpl.updateUserPermission(baseEntities, 1L);
        assertEquals(true, result);
    }

    // 更新分享权限：正常（无历史数据）
    @Test
    void updateSharePermission_01() {
        ResourcePermissionPO first = basePoList.getFirst();
        doReturn(List.of()).when(resourcePermissionPOMapper).selectPermissions(anyList());
        doReturn(true).when(stringRedisTemplate).delete("anyKey");
        doReturn(1).when(resourcePermissionPOMapper).batchInsertOrUpdatePermissions(anyList());

        Boolean result = resourcePermissionServiceImpl.updateUserSharePermission(first.getResourceId(), List.of(1L,2L), 1L);
        assertEquals(true, result);
    }

    // 更新分享权限：正常（有历史数据）
    @Test
    void updateSharePermission_02() {
        ResourcePermissionPO first = basePoList.getFirst();
        doReturn(List.of(first)).when(resourcePermissionPOMapper).selectPermissions(anyList());
        doReturn(true).when(stringRedisTemplate).delete("anyKey");
        doReturn(1).when(resourcePermissionPOMapper).batchInsertOrUpdatePermissions(anyList());

        Boolean result = resourcePermissionServiceImpl.updateUserSharePermission(first.getResourceId(), List.of(1L,2L), 1L);
        assertEquals(true, result);
    }

    // 获取平台所有教材待分配权限用户列表
    @Test
    void getUsersToShare(){
        // 参数
        ResourceUserSearchParam param = new ResourceUserSearchParam();
        param.setResourceId("RID_TEST_1");
        param.setKeyword("test");
        PageParams pageParams = new PageParams();
        pageParams.setPage(1);
        pageParams.setLimit(10);
        param.setPageParams(pageParams);
        // Mock
        List<UserInfo> newUserInfos = new ArrayList<>();
        UserInfo newUser1 = new UserInfo();
        newUser1.setId(1L);
        newUser1.setName("test");
        newUser1.setEnable(true);
        newUserInfos.add(newUser1);
        UserInfo newUser2 = new UserInfo();
        newUser2.setId(2L);
        newUser2.setName("test2");
        newUser2.setEnable(true);
        newUserInfos.add(newUser2);
        doReturn(newUserInfos).when(userServiceImpl).selectValidUsersByOrgAndKeyword(null, param.getKeyword(), pageParams);

        // 设置mock参数，检索分享用户和作者（作为未分享排除对象）
        Integer permissionValue = PermissionTypeEnum.SHARE.getCode() | PermissionTypeEnum.OWNER.getCode();
        ResourcePermissionPO existPermission = new ResourcePermissionPO();
        existPermission.setUserId(1L);
        existPermission.setResourceId("RID_TEST_1");
        existPermission.setPermission(permissionValue);
        List<ResourcePermissionPO> existPermissions = List.of(existPermission);
        doReturn(existPermissions).when(resourcePermissionPOMapper).
                selectPermission(param.getResourceId(), null, null, permissionValue);

        // 执行
        List<ResourceUser> usersToShares = resourcePermissionServiceImpl.getUsersToShare(param);

        List<ResourceUser> exceptUsersToShares = new ArrayList<>();
        ResourceUser exceptUser1 = new ResourceUser();
        exceptUser1.setUserId(2L);
        exceptUser1.setUserName("test2");
        exceptUser1.setMobile(null);
        exceptUsersToShares.add(exceptUser1);
        assertEquals(usersToShares.getFirst().getUserId(), exceptUsersToShares.getFirst().getUserId());
    }

}