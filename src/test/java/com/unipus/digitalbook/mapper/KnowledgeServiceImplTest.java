package com.unipus.digitalbook.mapper;

import com.unipus.digitalbook.dao.BookKnowledgeInfoMapper;
import com.unipus.digitalbook.model.po.knowledge.BookKnowledgeInfoPO;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Date;

@SpringBootTest
class KnowledgeServiceImplTest {
    @Resource
    private BookKnowledgeInfoMapper bookKnowledgeInfoMapper;

    @Test
    void saveQuestionGroup() {

    }

    @Test
    void testInsert() {
        BookKnowledgeInfoPO insertKnowledgeInfo = new BookKnowledgeInfoPO();
        insertKnowledgeInfo.setBookId("xxxxxx");
        insertKnowledgeInfo.setName("params.getName()");
        insertKnowledgeInfo.setDescription("params.getBackground()");
        insertKnowledgeInfo.setKnowledgeId("knowledgeId");
        insertKnowledgeInfo.setCreateBy(1071L);
        insertKnowledgeInfo.setUpdateBy(1071L);
        insertKnowledgeInfo.setCreateTime(new Date());
        insertKnowledgeInfo.setUpdateTime(new Date());
        bookKnowledgeInfoMapper.insertSelective(insertKnowledgeInfo);
    }
}